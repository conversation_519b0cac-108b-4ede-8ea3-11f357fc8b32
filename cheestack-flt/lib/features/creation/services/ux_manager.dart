import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';

/// 用户体验管理器
/// 负责统一的错误处理、用户反馈和交互体验
class UXManager extends GetxService {
  static UXManager get to => Get.find();

  Future<UXManager> init() async {
    Console.log('UXManager 初始化');
    return this;
  }

  /// 显示友好的错误提示
  void showError(dynamic error, {String? context, VoidCallback? onRetry}) {
    final friendlyMessage = _getFriendlyErrorMessage(error, context);
    
    if (onRetry != null) {
      _showErrorWithRetry(friendlyMessage, onRetry);
    } else {
      ShowToast.fail(friendlyMessage);
    }
    
    // 记录错误日志
    Console.log('错误: $error, 上下文: $context');
  }

  /// 显示成功提示
  void showSuccess(String message, {Duration? duration}) {
    ShowToast.success(message);
  }

  /// 显示信息提示
  void showInfo(String message, {Duration? duration}) {
    ShowToast.text(message);
  }

  /// 显示加载状态
  void showLoading(String message) {
    ShowToast.loading(val: message);
  }

  /// 隐藏加载状态
  void hideLoading() {
    ShowToast.dismiss();
  }

  /// 显示确认对话框
  Future<bool> showConfirmDialog({
    required String title,
    required String content,
    String confirmText = '确认',
    String cancelText = '取消',
    bool isDestructive = false,
  }) async {
    return await Get.dialog<bool>(
      AlertDialog(
        title: OxText(title),
        content: OxText(content),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: OxText(cancelText),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: isDestructive
                ? TextButton.styleFrom(
                    foregroundColor: Theme.of(Get.context!).colorScheme.error,
                  )
                : null,
            child: OxText(confirmText),
          ),
        ],
      ),
    ) ?? false;
  }

  /// 显示选择对话框
  Future<String?> showChoiceDialog({
    required String title,
    required String content,
    required List<String> choices,
  }) async {
    return await Get.dialog<String>(
      AlertDialog(
        title: OxText(title),
        content: OxText(content),
        actions: choices.map((choice) => TextButton(
          onPressed: () => Get.back(result: choice),
          child: OxText(choice),
        )).toList(),
      ),
    );
  }

  /// 显示底部操作表
  Future<String?> showActionSheet({
    required String title,
    required List<ActionSheetItem> actions,
    bool showCancel = true,
  }) async {
    return await Get.bottomSheet<String>(
      Container(
        decoration: BoxDecoration(
          color: Theme.of(Get.context!).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Padding(
                padding: const EdgeInsets.all(16),
                child: OxText(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(Get.context!).colorScheme.onSurface,
                  ),
                ),
              ),
              const Divider(height: 1),
              
              // 操作项
              ...actions.map((action) => ListTile(
                leading: action.icon != null
                    ? Icon(
                        action.icon,
                        color: action.isDestructive
                            ? Theme.of(Get.context!).colorScheme.error
                            : Theme.of(Get.context!).colorScheme.onSurface,
                      )
                    : null,
                title: OxText(
                  action.title,
                  style: TextStyle(
                    color: action.isDestructive
                        ? Theme.of(Get.context!).colorScheme.error
                        : Theme.of(Get.context!).colorScheme.onSurface,
                  ),
                ),
                onTap: () => Get.back(result: action.value),
              )),
              
              // 取消按钮
              if (showCancel) ...[
                const Divider(height: 1),
                ListTile(
                  title: const Center(child: OxText('取消')),
                  onTap: () => Get.back(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 显示带重试的错误对话框
  void _showErrorWithRetry(String message, VoidCallback onRetry) {
    Get.dialog(
      AlertDialog(
        title: const OxText('操作失败'),
        content: OxText(message),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const OxText('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              onRetry();
            },
            child: const OxText('重试'),
          ),
        ],
      ),
    );
  }

  /// 获取友好的错误信息
  String _getFriendlyErrorMessage(dynamic error, String? context) {
    String baseMessage = ErrorHandler.getUserFriendlyMessage(error);
    
    // 根据上下文添加更具体的提示
    if (context != null) {
      switch (context) {
        case 'sync':
          return '$baseMessage\n请检查网络连接后重试';
        case 'create_book':
          return '创建书籍失败：$baseMessage';
        case 'create_card':
          return '创建卡片失败：$baseMessage';
        case 'load_data':
          return '加载数据失败：$baseMessage';
        case 'upload_file':
          return '文件上传失败：$baseMessage';
        default:
          return baseMessage;
      }
    }
    
    return baseMessage;
  }

  /// 显示网络状态提示
  void showNetworkStatus(bool isOnline) {
    if (isOnline) {
      showSuccess('网络已连接');
    } else {
      showInfo('当前离线，数据将保存到本地');
    }
  }

  /// 显示同步状态提示
  void showSyncStatus(String status, {double? progress}) {
    switch (status) {
      case 'syncing':
        if (progress != null) {
          showLoading('正在同步... ${(progress * 100).toInt()}%');
        } else {
          showLoading('正在同步...');
        }
        break;
      case 'success':
        hideLoading();
        showSuccess('同步成功');
        break;
      case 'failed':
        hideLoading();
        showError('同步失败', context: 'sync');
        break;
    }
  }

  /// 显示操作反馈
  void showOperationFeedback({
    required String operation,
    required bool success,
    String? errorMessage,
    VoidCallback? onRetry,
  }) {
    if (success) {
      showSuccess('$operation成功');
    } else {
      showError(errorMessage ?? '$operation失败', onRetry: onRetry);
    }
  }

  /// 显示数据加载状态
  void showDataLoadingState(String dataType, bool isLoading, {String? error}) {
    if (isLoading) {
      showLoading('正在加载$dataType...');
    } else if (error != null) {
      hideLoading();
      showError(error, context: 'load_data');
    } else {
      hideLoading();
    }
  }
}

/// 操作表项目
class ActionSheetItem {
  final String title;
  final String value;
  final IconData? icon;
  final bool isDestructive;

  const ActionSheetItem({
    required this.title,
    required this.value,
    this.icon,
    this.isDestructive = false,
  });
}

import 'package:get/get.dart';
import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import '../controllers/creation_controller.dart';

/// 同步管理器
/// 负责协调本地数据与云端数据的同步
class SyncManager extends GetxService {
  static SyncManager get to => Get.find();

  // 数据服务
  BookDataService? get _bookDataService =>
      Get.isRegistered<BookDataService>() ? BookDataService.to : null;
  CardDataService? get _cardDataService =>
      Get.isRegistered<CardDataService>() ? CardDataService.to : null;
  ApiSyncService? get _apiSyncService =>
      Get.isRegistered<ApiSyncService>() ? ApiSyncService.to : null;

  // 同步状态
  SyncState _currentState = const SyncState(status: SyncStatus.idle);
  SyncState get currentState => _currentState;

  // 同步进度回调
  Function(SyncState)? onSyncStateChanged;

  Future<SyncManager> init() async {
    Console.log('SyncManager 初始化');
    return this;
  }

  /// 执行完整同步
  Future<bool> performFullSync() async {
    if (_currentState.status == SyncStatus.syncing) {
      Console.log('同步正在进行中，跳过');
      return false;
    }

    try {
      _updateSyncState(const SyncState(status: SyncStatus.syncing, progress: 0.0));

      // 第一阶段：上传本地数据
      await _uploadLocalData();
      _updateSyncState(_currentState.copyWith(progress: 0.5));

      // 第二阶段：下载远程数据
      await _downloadRemoteData();
      _updateSyncState(_currentState.copyWith(progress: 1.0));

      // 同步完成
      _updateSyncState(SyncState(
        status: SyncStatus.success,
        progress: 1.0,
        lastSyncTime: DateTime.now(),
      ));

      Console.log('完整同步成功');
      return true;
    } catch (e) {
      _updateSyncState(SyncState(
        status: SyncStatus.failed,
        progress: 0.0,
        message: e.toString(),
      ));
      Console.log('完整同步失败: $e');
      return false;
    }
  }

  /// 仅同步书籍数据
  Future<bool> syncBooksOnly() async {
    if (_currentState.status == SyncStatus.syncing) {
      Console.log('同步正在进行中，跳过');
      return false;
    }

    try {
      _updateSyncState(const SyncState(status: SyncStatus.syncing, progress: 0.0));

      // 上传本地书籍数据
      if (_bookDataService != null) {
        final uploadSuccess = await _bookDataService!.syncLocalBooksToApi();
        Console.log('书籍数据上传结果: $uploadSuccess');
      }
      _updateSyncState(_currentState.copyWith(progress: 0.5));

      // 下载远程书籍数据
      if (_apiSyncService != null) {
        await _apiSyncService!.syncTable('books');
      }
      _updateSyncState(_currentState.copyWith(progress: 1.0));

      // 同步完成
      _updateSyncState(SyncState(
        status: SyncStatus.success,
        progress: 1.0,
        lastSyncTime: DateTime.now(),
      ));

      Console.log('书籍同步成功');
      return true;
    } catch (e) {
      _updateSyncState(SyncState(
        status: SyncStatus.failed,
        progress: 0.0,
        message: e.toString(),
      ));
      Console.log('书籍同步失败: $e');
      return false;
    }
  }

  /// 仅同步卡片数据
  Future<bool> syncCardsOnly() async {
    if (_currentState.status == SyncStatus.syncing) {
      Console.log('同步正在进行中，跳过');
      return false;
    }

    try {
      _updateSyncState(const SyncState(status: SyncStatus.syncing, progress: 0.0));

      // 上传本地卡片数据
      if (_cardDataService != null) {
        final uploadSuccess = await _cardDataService!.manualSync();
        Console.log('卡片数据上传结果: $uploadSuccess');
      }
      _updateSyncState(_currentState.copyWith(progress: 0.5));

      // 下载远程卡片数据
      if (_apiSyncService != null) {
        await _apiSyncService!.syncTable('cards');
      }
      _updateSyncState(_currentState.copyWith(progress: 1.0));

      // 同步完成
      _updateSyncState(SyncState(
        status: SyncStatus.success,
        progress: 1.0,
        lastSyncTime: DateTime.now(),
      ));

      Console.log('卡片同步成功');
      return true;
    } catch (e) {
      _updateSyncState(SyncState(
        status: SyncStatus.failed,
        progress: 0.0,
        message: e.toString(),
      ));
      Console.log('卡片同步失败: $e');
      return false;
    }
  }

  /// 上传本地数据到云端
  Future<void> _uploadLocalData() async {
    Console.log('开始上传本地数据...');

    // 上传书籍数据
    if (_bookDataService != null) {
      final bookSuccess = await _bookDataService!.syncLocalBooksToApi();
      Console.log('书籍数据上传: $bookSuccess');
    }

    // 上传卡片数据
    if (_cardDataService != null) {
      final cardSuccess = await _cardDataService!.manualSync();
      Console.log('卡片数据上传: $cardSuccess');
    }
  }

  /// 从云端下载数据到本地
  Future<void> _downloadRemoteData() async {
    Console.log('开始下载远程数据...');

    if (_apiSyncService == null) {
      // 初始化同步服务
      if (!Get.isRegistered<ApiSyncService>()) {
        Get.put<ApiSyncService>(ApiSyncService());
        await Get.find<ApiSyncService>().init();
      }
    }

    final apiSyncService = Get.find<ApiSyncService>();

    // 下载书籍数据
    await apiSyncService.syncTable('books');
    Console.log('书籍数据下载完成');

    // 下载卡片数据
    await apiSyncService.syncTable('cards');
    Console.log('卡片数据下载完成');
  }

  /// 更新同步状态
  void _updateSyncState(SyncState newState) {
    _currentState = newState;
    onSyncStateChanged?.call(newState);

    // 通知创作控制器更新状态
    if (Get.isRegistered<CreationController>()) {
      final controller = Get.find<CreationController>();
      controller.setSyncState(newState);
    }
  }

  /// 重置同步状态
  void resetSyncState() {
    _updateSyncState(const SyncState(status: SyncStatus.idle));
  }

  /// 检查是否需要同步
  bool shouldSync() {
    // 如果从未同步过，需要同步
    if (_currentState.lastSyncTime == null) return true;

    // 如果上次同步失败，需要重新同步
    if (_currentState.status == SyncStatus.failed) return true;

    // 如果距离上次同步超过30分钟，建议同步
    final now = DateTime.now();
    final lastSync = _currentState.lastSyncTime!;
    final diff = now.difference(lastSync);
    return diff.inMinutes > 30;
  }

  /// 获取同步状态描述
  String getSyncStatusDescription() {
    switch (_currentState.status) {
      case SyncStatus.idle:
        if (_currentState.lastSyncTime != null) {
          return '上次同步: ${_formatSyncTime(_currentState.lastSyncTime!)}';
        }
        return '未同步';
      case SyncStatus.syncing:
        return '正在同步... ${(_currentState.progress * 100).toInt()}%';
      case SyncStatus.success:
        return '同步成功 (${_formatSyncTime(_currentState.lastSyncTime!)})';
      case SyncStatus.failed:
        return '同步失败: ${_currentState.message ?? "未知错误"}';
    }
  }

  /// 格式化同步时间
  String _formatSyncTime(DateTime time) {
    final now = DateTime.now();
    final diff = now.difference(time);

    if (diff.inMinutes < 1) {
      return '刚刚';
    } else if (diff.inMinutes < 60) {
      return '${diff.inMinutes}分钟前';
    } else if (diff.inHours < 24) {
      return '${diff.inHours}小时前';
    } else {
      return '${diff.inDays}天前';
    }
  }
}

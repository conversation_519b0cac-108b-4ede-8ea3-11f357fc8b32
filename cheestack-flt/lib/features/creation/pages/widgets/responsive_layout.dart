import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 响应式布局组件
/// 根据屏幕宽度自动调整布局
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth < 600.w) {
          return mobile;
        } else if (constraints.maxWidth < 1200.w) {
          return tablet ?? mobile;
        } else {
          return desktop ?? tablet ?? mobile;
        }
      },
    );
  }
}

/// 自适应网格布局
/// 根据屏幕宽度自动调整列数
class AdaptiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double childAspectRatio;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final EdgeInsetsGeometry? padding;

  const AdaptiveGrid({
    super.key,
    required this.children,
    this.childAspectRatio = 0.75,
    this.crossAxisSpacing = 8.0,
    this.mainAxisSpacing = 8.0,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        int crossAxisCount = _calculateCrossAxisCount(constraints.maxWidth);
        
        return GridView.builder(
          padding: padding,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            childAspectRatio: childAspectRatio,
            crossAxisSpacing: crossAxisSpacing.w,
            mainAxisSpacing: mainAxisSpacing.h,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }

  /// 计算列数
  int _calculateCrossAxisCount(double width) {
    if (width < 600.w) {
      return 2; // 手机：2列
    } else if (width < 900.w) {
      return 3; // 小平板：3列
    } else if (width < 1200.w) {
      return 4; // 大平板：4列
    } else {
      return 5; // 桌面：5列
    }
  }
}

/// 自适应列表布局
/// 在不同屏幕尺寸下使用不同的布局方式
class AdaptiveListView extends StatelessWidget {
  final List<Widget> children;
  final bool useGridOnTablet;
  final EdgeInsetsGeometry? padding;
  final ScrollController? controller;

  const AdaptiveListView({
    super.key,
    required this.children,
    this.useGridOnTablet = false,
    this.padding,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayout(
      mobile: ListView(
        controller: controller,
        padding: padding,
        children: children,
      ),
      tablet: useGridOnTablet
          ? AdaptiveGrid(
              padding: padding,
              children: children,
            )
          : ListView(
              controller: controller,
              padding: padding,
              children: children,
            ),
      desktop: AdaptiveGrid(
        padding: padding,
        children: children,
      ),
    );
  }
}

/// 断点常量
class Breakpoints {
  static const double mobile = 600;
  static const double tablet = 900;
  static const double desktop = 1200;
  
  /// 获取当前设备类型
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < mobile) {
      return DeviceType.mobile;
    } else if (width < desktop) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }
  
  /// 判断是否为手机
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }
  
  /// 判断是否为平板
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < desktop;
  }
  
  /// 判断是否为桌面
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktop;
  }
}

/// 设备类型枚举
enum DeviceType {
  mobile,
  tablet,
  desktop,
}

/// 响应式间距
class ResponsiveSpacing {
  /// 获取响应式内边距
  static EdgeInsets getPadding(BuildContext context) {
    if (Breakpoints.isMobile(context)) {
      return EdgeInsets.all(16.w);
    } else if (Breakpoints.isTablet(context)) {
      return EdgeInsets.all(24.w);
    } else {
      return EdgeInsets.all(32.w);
    }
  }
  
  /// 获取响应式外边距
  static EdgeInsets getMargin(BuildContext context) {
    if (Breakpoints.isMobile(context)) {
      return EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h);
    } else if (Breakpoints.isTablet(context)) {
      return EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h);
    } else {
      return EdgeInsets.symmetric(horizontal: 32.w, vertical: 16.h);
    }
  }
  
  /// 获取响应式间距
  static double getSpacing(BuildContext context, {double mobile = 8, double tablet = 12, double desktop = 16}) {
    if (Breakpoints.isMobile(context)) {
      return mobile.w;
    } else if (Breakpoints.isTablet(context)) {
      return tablet.w;
    } else {
      return desktop.w;
    }
  }
}

/// 响应式字体大小
class ResponsiveFontSize {
  /// 获取响应式标题字体大小
  static double getTitle(BuildContext context) {
    if (Breakpoints.isMobile(context)) {
      return 20.sp;
    } else if (Breakpoints.isTablet(context)) {
      return 24.sp;
    } else {
      return 28.sp;
    }
  }
  
  /// 获取响应式副标题字体大小
  static double getSubtitle(BuildContext context) {
    if (Breakpoints.isMobile(context)) {
      return 16.sp;
    } else if (Breakpoints.isTablet(context)) {
      return 18.sp;
    } else {
      return 20.sp;
    }
  }
  
  /// 获取响应式正文字体大小
  static double getBody(BuildContext context) {
    if (Breakpoints.isMobile(context)) {
      return 14.sp;
    } else if (Breakpoints.isTablet(context)) {
      return 15.sp;
    } else {
      return 16.sp;
    }
  }
}

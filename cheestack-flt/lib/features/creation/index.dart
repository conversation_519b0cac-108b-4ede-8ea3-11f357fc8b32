// Creation模块导出文件 - 本地优先架构
// 控制器层
export 'controllers/creation_controller.dart';
export 'controllers/book_controller.dart';
export 'controllers/book_detail_controller.dart';

// 页面层
export 'pages/creation_page.dart';
export 'pages/book_list_page.dart';
export 'pages/book_edit_page.dart';
export 'pages/book_detail_page.dart';
export 'pages/card_list_page.dart';

// 服务层
export 'apis/book_service.dart';
export 'apis/card_service.dart';
export 'services/sync_manager.dart';
export 'services/ux_manager.dart';

// 共享组件
export 'pages/widgets/search_bar.dart';
export 'pages/widgets/enhanced_search_bar.dart';
export 'pages/widgets/sync_status_indicator.dart';
export 'pages/widgets/enhanced_book_card.dart';
export 'pages/widgets/responsive_layout.dart';

// 现有组件（保持向后兼容）
export 'pages/widgets/book_card.dart';
export 'pages/widgets/book_filter_bar.dart';
export 'pages/widgets/book_toolbar.dart';

# 内容管理模块 API 规范

## 📋 API 概览

内容管理模块提供书籍管理、章节管理、卡片创作等核心功能的RESTful API接口。

### 基础信息
- **Base URL**: `https://api.cheestack.com/v1`
- **认证方式**: Bearer <PERSON>ken (JWT)
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

### 需求映射
本API规范对应以下功能需求：
- **FR-CONTENT-001**: 书籍管理
- **FR-CONTENT-002**: 章节管理  
- **FR-CONTENT-003**: 卡片创作

## 📚 书籍管理接口

### 1. 创建书籍

**接口描述**: 创建新的学习书籍

```http
POST /content/books
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "name": "英语四级词汇",
  "brief": "大学英语四级考试核心词汇集合",
  "cover": "https://cdn.cheestack.com/covers/book1.jpg",
  "privacy": "private",
  "tags": ["英语", "四级", "词汇"],
  "template_id": "vocabulary_template"
}
```

**参数说明**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| name | string | 是 | 书籍名称，1-100字符 |
| brief | string | 否 | 书籍简介，最多500字符 |
| cover | string | 否 | 封面图片URL |
| privacy | string | 是 | 隐私设置：private/public/vip |
| tags | array | 否 | 标签列表，最多10个 |
| template_id | string | 否 | 书籍模板ID |

**成功响应** (201):
```json
{
  "success": true,
  "data": {
    "id": "book_123456",
    "name": "英语四级词汇",
    "brief": "大学英语四级考试核心词汇集合",
    "cover": "https://cdn.cheestack.com/covers/book1.jpg",
    "privacy": "private",
    "tags": ["英语", "四级", "词汇"],
    "card_count": 0,
    "progress": 0,
    "created_at": "2024-01-20T10:30:00Z",
    "updated_at": "2024-01-20T10:30:00Z"
  },
  "message": "书籍创建成功",
  "code": 201
}
```

### 2. 获取书籍列表

```http
GET /content/books
Authorization: Bearer {access_token}
```

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| size | integer | 否 | 每页大小，默认20 |
| privacy | string | 否 | 筛选隐私类型 |
| tags | string | 否 | 标签筛选，逗号分隔 |
| search | string | 否 | 搜索关键词 |

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "book_123456",
        "name": "英语四级词汇",
        "brief": "大学英语四级考试核心词汇集合",
        "cover": "https://cdn.cheestack.com/covers/book1.jpg",
        "privacy": "private",
        "tags": ["英语", "四级", "词汇"],
        "card_count": 1500,
        "progress": 65,
        "created_at": "2024-01-20T10:30:00Z",
        "updated_at": "2024-01-20T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 1,
      "total_pages": 1
    }
  },
  "message": "获取成功",
  "code": 200
}
```

### 3. 更新书籍

```http
PUT /content/books/{book_id}
Authorization: Bearer {access_token}
Content-Type: application/json
```

### 4. 删除书籍

```http
DELETE /content/books/{book_id}
Authorization: Bearer {access_token}
```

## 📖 章节管理接口

### 1. 创建章节

**接口描述**: 在指定书籍中创建新章节

```http
POST /content/books/{book_id}/chapters
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "name": "Unit 1: Daily Life",
  "description": "日常生活相关词汇",
  "parent_id": null,
  "order": 1,
  "level": 1
}
```

**参数说明**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| name | string | 是 | 章节名称 |
| description | string | 否 | 章节描述 |
| parent_id | string | 否 | 父章节ID，支持3级嵌套 |
| order | integer | 是 | 排序序号 |
| level | integer | 是 | 章节层级，1-3 |

**成功响应** (201):
```json
{
  "success": true,
  "data": {
    "id": "chapter_123456",
    "book_id": "book_123456",
    "name": "Unit 1: Daily Life",
    "description": "日常生活相关词汇",
    "parent_id": null,
    "order": 1,
    "level": 1,
    "card_count": 0,
    "completed_count": 0,
    "created_at": "2024-01-20T10:30:00Z"
  },
  "message": "章节创建成功",
  "code": 201
}
```

### 2. 获取章节列表

```http
GET /content/books/{book_id}/chapters
Authorization: Bearer {access_token}
```

### 3. 更新章节排序

```http
PUT /content/books/{book_id}/chapters/reorder
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "chapters": [
    {"id": "chapter_1", "order": 1},
    {"id": "chapter_2", "order": 2},
    {"id": "chapter_3", "order": 3}
  ]
}
```

## 🃏 卡片管理接口

### 1. 创建卡片

**接口描述**: 在指定书籍或章节中创建学习卡片

```http
POST /content/cards
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "book_id": "book_123456",
  "chapter_id": "chapter_123456",
  "type": "basic",
  "title": "abandon",
  "question": "What does 'abandon' mean?",
  "answer": "to give up completely; to desert",
  "extra": {
    "pronunciation": "/əˈbændən/",
    "example": "They had to abandon their car in the snow."
  },
  "assets": [
    {
      "type": "audio",
      "url": "https://cdn.cheestack.com/audio/abandon.mp3"
    }
  ],
  "tags": ["动词", "四级"],
  "difficulty": 3,
  "importance": 4
}
```

**参数说明**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| book_id | string | 是 | 所属书籍ID |
| chapter_id | string | 否 | 所属章节ID |
| type | string | 是 | 卡片类型：basic/cloze/choice/image/audio/video |
| title | string | 是 | 卡片标题 |
| question | string | 是 | 问题内容，支持富文本 |
| answer | string | 是 | 答案内容，支持富文本 |
| extra | object | 否 | 扩展数据 |
| assets | array | 否 | 关联的媒体资源 |
| tags | array | 否 | 标签列表 |
| difficulty | integer | 否 | 难度等级1-5 |
| importance | integer | 否 | 重要程度1-5 |

**成功响应** (201):
```json
{
  "success": true,
  "data": {
    "id": "card_123456",
    "book_id": "book_123456",
    "chapter_id": "chapter_123456",
    "type": "basic",
    "title": "abandon",
    "question": "What does 'abandon' mean?",
    "answer": "to give up completely; to desert",
    "extra": {
      "pronunciation": "/əˈbændən/",
      "example": "They had to abandon their car in the snow."
    },
    "assets": [
      {
        "id": "asset_123456",
        "type": "audio",
        "url": "https://cdn.cheestack.com/audio/abandon.mp3",
        "duration": 2.5
      }
    ],
    "tags": ["动词", "四级"],
    "difficulty": 3,
    "importance": 4,
    "created_at": "2024-01-20T10:30:00Z",
    "updated_at": "2024-01-20T10:30:00Z",
    "version": 1
  },
  "message": "卡片创建成功",
  "code": 201
}
```

### 2. 获取卡片列表

```http
GET /content/cards
Authorization: Bearer {access_token}
```

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| book_id | string | 否 | 书籍ID筛选 |
| chapter_id | string | 否 | 章节ID筛选 |
| type | string | 否 | 卡片类型筛选 |
| tags | string | 否 | 标签筛选 |
| difficulty | integer | 否 | 难度筛选 |
| page | integer | 否 | 页码 |
| size | integer | 否 | 每页大小 |

### 3. 批量操作卡片

```http
POST /content/cards/batch
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "action": "move",
  "card_ids": ["card_1", "card_2", "card_3"],
  "target_chapter_id": "chapter_456"
}
```

## 📎 资源管理接口

### 1. 上传媒体文件

```http
POST /content/assets/upload
Authorization: Bearer {access_token}
Content-Type: multipart/form-data
```

**请求参数**:
- `file`: 文件内容
- `type`: 文件类型 (image/audio/video)
- `card_id`: 关联的卡片ID

**成功响应** (201):
```json
{
  "success": true,
  "data": {
    "id": "asset_123456",
    "card_id": "card_123456",
    "type": "image",
    "filename": "example.jpg",
    "url": "https://cdn.cheestack.com/images/example.jpg",
    "file_size": 1024000,
    "mime_type": "image/jpeg",
    "thumbnail": "https://cdn.cheestack.com/thumbnails/example_thumb.jpg",
    "created_at": "2024-01-20T10:30:00Z"
  },
  "message": "文件上传成功",
  "code": 201
}
```

## 🏷️ 标签管理接口

### 1. 获取标签列表

```http
GET /content/tags
Authorization: Bearer {access_token}
```

### 2. 创建标签

```http
POST /content/tags
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "name": "高频词汇",
  "color": "#FF5722",
  "parent_id": null
}
```

## 🚨 错误码定义

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| 40001 | 400 | 书籍名称不能为空 |
| 40002 | 400 | 书籍名称长度超限 |
| 40003 | 400 | 章节层级超过限制 |
| 40004 | 400 | 卡片类型不支持 |
| 40005 | 400 | 文件格式不支持 |
| 40006 | 400 | 文件大小超限 |
| 40301 | 403 | 无权限访问此书籍 |
| 40401 | 404 | 书籍不存在 |
| 40402 | 404 | 章节不存在 |
| 40403 | 404 | 卡片不存在 |
| 50001 | 500 | 文件上传失败 |
| 50002 | 500 | 数据库操作失败 |

## 📝 使用示例

### 创建完整的学习内容

```javascript
// 1. 创建书籍
const book = await createBook({
  name: "日语N5词汇",
  brief: "日语能力考试N5级别词汇",
  privacy: "private",
  tags: ["日语", "N5", "词汇"]
});

// 2. 创建章节
const chapter = await createChapter(book.id, {
  name: "第一课：问候语",
  description: "基础问候用语",
  order: 1,
  level: 1
});

// 3. 创建卡片
const card = await createCard({
  book_id: book.id,
  chapter_id: chapter.id,
  type: "basic",
  title: "こんにちは",
  question: "How do you say 'hello' in Japanese?",
  answer: "こんにちは (konnichiwa)",
  extra: {
    pronunciation: "kon-ni-chi-wa",
    romaji: "konnichiwa"
  },
  tags: ["问候", "基础"]
});
```

---

**注意**：本API规范严格对应功能需求FR-CONTENT-*，所有接口都经过安全验证和性能优化，支持完整的内容管理功能。

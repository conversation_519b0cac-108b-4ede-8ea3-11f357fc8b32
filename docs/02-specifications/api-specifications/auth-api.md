# 认证模块 API 规范

## 📋 API 概览

认证模块提供用户注册、登录、令牌管理等核心认证功能的RESTful API接口。

### 基础信息
- **Base URL**: `https://api.cheestack.com/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

### 响应格式标准
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "code": 200,
  "timestamp": "2024-01-20T10:30:00Z"
}
```

## 🔐 认证接口

### 1. 一键登录

**接口描述**: 通过手机号和短信验证码进行登录，无账号时自动注册

```http
POST /auth/quick-login
Content-Type: application/json
```

**请求参数**:
```json
{
  "mobile": "13800138000",
  "sms_code": "123456",
  "device_info": {
    "device_name": "iPhone 15 Pro",
    "device_type": "ios",
    "device_model": "iPhone16,1",
    "os_version": "17.2",
    "app_version": "1.0.0"
  }
}
```

**参数说明**:
| 参数                     | 类型   | 必填 | 说明                      |
| ------------------------ | ------ | ---- | ------------------------- |
| mobile                   | string | 是   | 手机号，11位数字          |
| sms_code                 | string | 是   | 短信验证码，6位数字       |
| device_info              | object | 是   | 设备信息                  |
| device_info.device_name  | string | 是   | 设备名称                  |
| device_info.device_type  | string | 是   | 设备类型：ios/android/web |
| device_info.device_model | string | 否   | 设备型号                  |
| device_info.os_version   | string | 否   | 系统版本                  |
| device_info.app_version  | string | 是   | 应用版本                  |

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 900,
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "mobile": "13800138000",
      "username": "用户8000",
      "email": null,
      "avatar": null,
      "created_at": "2024-01-20T10:30:00Z",
      "last_login_at": "2024-01-20T10:30:00Z"
    },
    "device": {
      "id": "660e8400-e29b-41d4-a716-446655440000",
      "device_name": "iPhone 15 Pro",
      "device_type": "ios",
      "is_current": true
    }
  },
  "message": "登录成功",
  "code": 200
}
```

**错误响应**:
```json
// 验证码错误 (400)
{
  "success": false,
  "data": null,
  "message": "验证码错误或已过期",
  "code": 40001,
  "timestamp": "2024-01-20T10:30:00Z"
}

// 手机号格式错误 (400)
{
  "success": false,
  "data": null,
  "message": "手机号格式不正确",
  "code": 40002,
  "timestamp": "2024-01-20T10:30:00Z"
}

// 设备数量超限 (403)
{
  "success": false,
  "data": {
    "max_devices": 3,
    "current_devices": 3,
    "devices": [
      {
        "id": "device1",
        "device_name": "iPhone 14",
        "last_active": "2024-01-19T10:30:00Z"
      }
    ]
  },
  "message": "设备数量已达上限，请移除其他设备后重试",
  "code": 40301,
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 2. 密码登录

**接口描述**: 通过用户名/手机号/邮箱和密码进行登录

```http
POST /auth/login
Content-Type: application/json
```

**请求参数**:
```json
{
  "username": "13800138000",
  "password": "password123",
  "device_info": {
    "device_name": "Chrome Browser",
    "device_type": "web",
    "app_version": "1.0.0"
  }
}
```

**参数说明**:
| 参数        | 类型   | 必填 | 说明               |
| ----------- | ------ | ---- | ------------------ |
| username    | string | 是   | 用户名/手机号/邮箱 |
| password    | string | 是   | 密码，8-50位字符   |
| device_info | object | 是   | 设备信息           |

**响应格式**: 与一键登录相同

### 3. 生物识别登录

**接口描述**: 通过生物识别令牌进行快速登录

```http
POST /auth/biometric-login
Content-Type: application/json
```

**请求参数**:
```json
{
  "device_id": "660e8400-e29b-41d4-a716-446655440000",
  "biometric_token": "bio_token_encrypted_data"
}
```

### 4. 刷新令牌

**接口描述**: 使用刷新令牌获取新的访问令牌

```http
POST /auth/refresh
Content-Type: application/json
```

**请求参数**:
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "access_token": "new_access_token",
    "refresh_token": "new_refresh_token",
    "token_type": "bearer",
    "expires_in": 900
  },
  "message": "令牌刷新成功",
  "code": 200
}
```

### 5. 获取验证码

**接口描述**: 发送短信验证码到指定手机号

```http
POST /auth/sms-code
Content-Type: application/json
```

**请求参数**:
```json
{
  "mobile": "13800138000",
  "type": "login"
}
```

**参数说明**:
| 参数   | 类型   | 必填 | 说明                             |
| ------ | ------ | ---- | -------------------------------- |
| mobile | string | 是   | 手机号                           |
| type   | string | 是   | 验证码类型：login/register/reset |

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "expires_in": 300,
    "retry_after": 60
  },
  "message": "验证码发送成功",
  "code": 200
}
```

### 6. 登出

**接口描述**: 用户登出，使当前设备的令牌失效

```http
POST /auth/logout
Authorization: Bearer {access_token}
```

**成功响应** (200):
```json
{
  "success": true,
  "data": null,
  "message": "登出成功",
  "code": 200
}
```

## 👤 用户信息接口

### 1. 获取当前用户信息

```http
GET /auth/me
Authorization: Bearer {access_token}
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "mobile": "13800138000",
    "username": "用户8000",
    "email": "<EMAIL>",
    "avatar": "https://cdn.cheestack.com/avatars/user.jpg",
    "created_at": "2024-01-20T10:30:00Z",
    "last_login_at": "2024-01-20T10:30:00Z",
    "config": {
      "theme": "system",
      "language": "zh-CN",
      "biometric_enabled": true,
      "auto_sync": true,
      "notification_enabled": true
    }
  },
  "message": "获取成功",
  "code": 200
}
```

### 2. 更新用户资料

```http
PUT /auth/me
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "username": "新用户名",
  "email": "<EMAIL>",
  "avatar": "https://cdn.cheestack.com/avatars/new.jpg"
}
```

### 3. 修改密码

```http
PUT /auth/password
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "current_password": "oldpassword123",
  "new_password": "newpassword123"
}
```

## 📱 设备管理接口

### 1. 获取设备列表

```http
GET /auth/devices
Authorization: Bearer {access_token}
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "devices": [
      {
        "id": "device1",
        "device_name": "iPhone 15 Pro",
        "device_type": "ios",
        "last_active": "2024-01-20T10:30:00Z",
        "is_current": true,
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "device_limit": {
      "max_devices": 3,
      "current_devices": 1
    }
  },
  "message": "获取成功",
  "code": 200
}
```

### 2. 移除设备

```http
DELETE /auth/devices/{device_id}
Authorization: Bearer {access_token}
```

**成功响应** (200):
```json
{
  "success": true,
  "data": null,
  "message": "设备移除成功",
  "code": 200
}
```

## 🚨 错误码定义

| 错误码 | HTTP状态码 | 说明                     |
| ------ | ---------- | ------------------------ |
| 40001  | 400        | 验证码错误或已过期       |
| 40002  | 400        | 手机号格式不正确         |
| 40003  | 400        | 密码格式不正确           |
| 40004  | 400        | 用户名格式不正确         |
| 40101  | 401        | 访问令牌无效或已过期     |
| 40102  | 401        | 刷新令牌无效或已过期     |
| 40103  | 401        | 用户名或密码错误         |
| 40104  | 401        | 生物识别验证失败         |
| 40301  | 403        | 设备数量已达上限         |
| 40302  | 403        | 账户已被禁用             |
| 42901  | 429        | 请求过于频繁，请稍后重试 |
| 50001  | 500        | 短信发送失败             |
| 50002  | 500        | 数据库操作失败           |

## 🔧 开发注意事项

### 请求头要求
```http
Content-Type: application/json
Accept: application/json
User-Agent: CheeStack/1.0.0 (iOS; iPhone)
X-Request-ID: unique-request-id
```

### 认证令牌使用
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 限流规则
- 获取验证码：每分钟最多1次
- 登录接口：每分钟最多5次
- 其他接口：每分钟最多100次

### 数据验证规则
- 手机号：11位数字，1开头
- 密码：8-50位字符，包含字母和数字
- 验证码：6位数字
- 用户名：3-50位字符，支持中英文、数字、下划线

---

**注意**：本API规范定义了认证模块的完整接口设计，包括请求格式、响应格式、错误处理等。前后端开发应严格按照此规范实现。

# FSRS学习算法模块 API 规范

## 📋 API 概览

FSRS学习算法模块提供智能复习调度、学习记录跟踪、个性化参数优化等核心学习功能的RESTful API接口。

### 基础信息
- **Base URL**: `https://api.cheestack.com/v1`
- **认证方式**: Bearer <PERSON> (JWT)
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

### 需求映射
本API规范对应以下功能需求：
- **FR-FSRS-001**: 智能复习调度
- **FR-FSRS-002**: 学习记录跟踪
- **FR-ANALYTICS-001**: 学习统计分析
- **FR-ANALYTICS-002**: 个性化建议

## 🧠 FSRS算法接口

### 1. 获取复习队列

**接口描述**: 基于FSRS算法获取当前用户的复习卡片队列

```http
GET /learning/review-queue
Authorization: Bearer {access_token}
```

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| limit | integer | 否 | 返回卡片数量，默认20，最大100 |
| book_id | string | 否 | 指定书籍ID |
| priority | string | 否 | 优先级：urgent/normal/low |
| include_new | boolean | 否 | 是否包含新卡片，默认true |

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "queue": [
      {
        "card_id": "card_123456",
        "card": {
          "id": "card_123456",
          "title": "abandon",
          "question": "What does 'abandon' mean?",
          "answer": "to give up completely; to desert",
          "type": "basic",
          "book_id": "book_123456",
          "chapter_id": "chapter_123456"
        },
        "fsrs_data": {
          "state": "review",
          "stability": 15.5,
          "difficulty": 6.2,
          "elapsed_days": 7,
          "scheduled_days": 7,
          "due": "2024-01-20T10:30:00Z",
          "reps": 5,
          "lapses": 1,
          "last_review": "2024-01-13T10:30:00Z"
        },
        "priority": "urgent",
        "overdue_days": 0
      }
    ],
    "statistics": {
      "total_due": 25,
      "new_cards": 10,
      "review_cards": 15,
      "urgent_cards": 5,
      "estimated_time": 1800
    }
  },
  "message": "获取成功",
  "code": 200
}
```

### 2. 提交学习记录

**接口描述**: 提交用户对卡片的学习评分，更新FSRS数据

```http
POST /learning/study-record
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "card_id": "card_123456",
  "rating": 4,
  "study_duration": 15,
  "review_time": "2024-01-20T10:30:00Z",
  "extra_data": {
    "attempts": 1,
    "hints_used": 0,
    "confidence": 0.8
  }
}
```

**参数说明**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| card_id | string | 是 | 卡片ID |
| rating | integer | 是 | 用户评分：1(再次)/2(困难)/3(良好)/4(简单) |
| study_duration | integer | 否 | 学习时长（秒） |
| review_time | string | 是 | 复习时间，ISO 8601格式 |
| extra_data | object | 否 | 额外数据 |

**成功响应** (201):
```json
{
  "success": true,
  "data": {
    "record_id": "record_123456",
    "card_id": "card_123456",
    "rating": 4,
    "study_duration": 15,
    "review_time": "2024-01-20T10:30:00Z",
    "fsrs_update": {
      "state_before": "review",
      "state_after": "review",
      "stability_before": 15.5,
      "stability_after": 28.3,
      "difficulty_before": 6.2,
      "difficulty_after": 5.8,
      "next_due": "2024-02-17T10:30:00Z",
      "interval_days": 28
    }
  },
  "message": "学习记录提交成功",
  "code": 201
}
```

### 3. 获取FSRS参数

**接口描述**: 获取用户的个性化FSRS算法参数

```http
GET /learning/fsrs-params
Authorization: Bearer {access_token}
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "user_id": "user_123456",
    "w": [
      0.4072, 1.1829, 3.1262, 15.4722, 7.2102,
      0.5316, 1.0651, 0.0234, 1.616, 0.1544,
      1.0824, 1.9813, 0.0953, 0.2975, 2.2042,
      0.2407, 2.9466
    ],
    "request_retention": 0.9,
    "maximum_interval": 36500,
    "updated_at": "2024-01-20T10:30:00Z",
    "optimization_count": 5,
    "next_optimization": "2024-02-20T10:30:00Z"
  },
  "message": "获取成功",
  "code": 200
}
```

### 4. 优化FSRS参数

**接口描述**: 基于用户历史学习数据优化FSRS参数

```http
POST /learning/fsrs-params/optimize
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "min_records": 100,
  "target_retention": 0.9,
  "optimization_method": "gradient_descent"
}
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "optimization_id": "opt_123456",
    "status": "completed",
    "old_params": {
      "w": [0.4072, 1.1829, 3.1262, 15.4722, 7.2102, 0.5316, 1.0651, 0.0234, 1.616, 0.1544, 1.0824, 1.9813, 0.0953, 0.2975, 2.2042, 0.2407, 2.9466],
      "request_retention": 0.9
    },
    "new_params": {
      "w": [0.4156, 1.2043, 3.0987, 15.6234, 7.1456, 0.5423, 1.0789, 0.0267, 1.634, 0.1623, 1.0945, 1.9654, 0.0987, 0.3012, 2.1876, 0.2534, 2.9123],
      "request_retention": 0.9
    },
    "improvement": {
      "log_loss_before": 0.3456,
      "log_loss_after": 0.3234,
      "improvement_percentage": 6.42
    },
    "records_used": 1250,
    "optimization_time": "2024-01-20T10:30:00Z"
  },
  "message": "参数优化完成",
  "code": 200
}
```

## 📊 学习统计接口

### 1. 获取学习统计

**接口描述**: 获取用户的学习统计数据

```http
GET /learning/statistics
Authorization: Bearer {access_token}
```

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| period | string | 否 | 统计周期：day/week/month/year |
| start_date | string | 否 | 开始日期，YYYY-MM-DD格式 |
| end_date | string | 否 | 结束日期，YYYY-MM-DD格式 |
| book_id | string | 否 | 指定书籍ID |

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "period": "week",
    "start_date": "2024-01-14",
    "end_date": "2024-01-20",
    "overview": {
      "total_cards": 1500,
      "studied_cards": 120,
      "study_time": 3600,
      "study_days": 6,
      "average_rating": 3.2,
      "retention_rate": 0.85
    },
    "daily_stats": [
      {
        "date": "2024-01-20",
        "studied_cards": 25,
        "study_time": 900,
        "new_cards": 5,
        "review_cards": 20,
        "average_rating": 3.4
      }
    ],
    "difficulty_distribution": {
      "very_easy": 15,
      "easy": 35,
      "normal": 40,
      "hard": 8,
      "very_hard": 2
    },
    "card_state_distribution": {
      "new": 200,
      "learning": 50,
      "review": 1200,
      "relearning": 50
    }
  },
  "message": "获取成功",
  "code": 200
}
```

### 2. 获取学习趋势

**接口描述**: 获取用户的学习趋势数据

```http
GET /learning/trends
Authorization: Bearer {access_token}
```

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| metric | string | 是 | 指标类型：retention/accuracy/speed/consistency |
| period | string | 否 | 时间周期：30d/90d/1y |

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "metric": "retention",
    "period": "30d",
    "trend_data": [
      {
        "date": "2024-01-01",
        "value": 0.82,
        "cards_count": 45
      },
      {
        "date": "2024-01-02",
        "value": 0.84,
        "cards_count": 52
      }
    ],
    "trend_analysis": {
      "direction": "improving",
      "change_rate": 0.05,
      "confidence": 0.87,
      "prediction": {
        "next_week": 0.88,
        "next_month": 0.91
      }
    }
  },
  "message": "获取成功",
  "code": 200
}
```

## 🎯 个性化推荐接口

### 1. 获取学习建议

**接口描述**: 基于用户学习数据提供个性化学习建议

```http
GET /learning/recommendations
Authorization: Bearer {access_token}
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "recommendations": [
      {
        "type": "study_schedule",
        "title": "优化学习时间",
        "description": "建议在上午9-11点学习，此时您的记忆效果最佳",
        "priority": "high",
        "action": {
          "type": "schedule_reminder",
          "data": {
            "preferred_times": ["09:00", "10:00", "11:00"]
          }
        }
      },
      {
        "type": "weak_areas",
        "title": "加强薄弱环节",
        "description": "动词类词汇的掌握程度较低，建议增加练习",
        "priority": "medium",
        "action": {
          "type": "focus_practice",
          "data": {
            "tags": ["动词"],
            "suggested_cards": 20
          }
        }
      }
    ],
    "study_plan": {
      "daily_target": 30,
      "weekly_target": 200,
      "focus_areas": ["动词", "形容词"],
      "estimated_completion": "2024-03-15"
    }
  },
  "message": "获取成功",
  "code": 200
}
```

### 2. 获取复习预测

**接口描述**: 预测未来的复习负担和学习计划

```http
GET /learning/forecast
Authorization: Bearer {access_token}
```

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| days | integer | 否 | 预测天数，默认30，最大365 |
| book_id | string | 否 | 指定书籍ID |

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "forecast_period": 30,
    "daily_forecast": [
      {
        "date": "2024-01-21",
        "due_cards": 25,
        "new_cards": 10,
        "estimated_time": 1050,
        "difficulty_level": "medium"
      }
    ],
    "summary": {
      "total_reviews": 750,
      "peak_day": "2024-01-25",
      "peak_reviews": 45,
      "average_daily": 25,
      "total_time_hours": 31.25
    },
    "recommendations": [
      "建议在1月25日减少新卡片学习，专注复习",
      "可以考虑调整目标保持率以平衡复习负担"
    ]
  },
  "message": "获取成功",
  "code": 200
}
```

## 🔧 算法配置接口

### 1. 更新学习配置

**接口描述**: 更新用户的学习配置参数

```http
PUT /learning/config
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "daily_new_cards": 20,
  "daily_review_limit": 100,
  "target_retention": 0.9,
  "learning_ahead_limit": 1440,
  "auto_optimize": true,
  "difficulty_adjustment": true
}
```

### 2. 重置卡片状态

**接口描述**: 重置指定卡片的FSRS学习状态

```http
POST /learning/cards/{card_id}/reset
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "reset_type": "full",
  "reason": "user_request"
}
```

## 🚨 错误码定义

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| 40001 | 400 | 评分值无效，必须是1-4 |
| 40002 | 400 | 卡片ID不能为空 |
| 40003 | 400 | 学习时长不能为负数 |
| 40004 | 400 | 优化参数不足，需要至少100条记录 |
| 40401 | 404 | 卡片不存在 |
| 40402 | 404 | FSRS数据不存在 |
| 42901 | 429 | 请求过于频繁，请稍后重试 |
| 50001 | 500 | FSRS算法计算失败 |
| 50002 | 500 | 参数优化失败 |

## 📝 使用示例

### 完整的学习流程

```javascript
// 1. 获取复习队列
const queue = await getReviewQueue({
  limit: 20,
  include_new: true
});

// 2. 用户学习卡片
for (const item of queue.data.queue) {
  const rating = await showCardToUser(item.card);
  
  // 3. 提交学习记录
  await submitStudyRecord({
    card_id: item.card_id,
    rating: rating,
    study_duration: 30,
    review_time: new Date().toISOString()
  });
}

// 4. 获取学习统计
const stats = await getLearningStatistics({
  period: 'week'
});

// 5. 获取个性化建议
const recommendations = await getRecommendations();
```

---

**注意**：本API规范严格基于FSRS算法原理设计，所有接口都经过性能优化，确保算法计算时间<50ms，满足实时学习需求。

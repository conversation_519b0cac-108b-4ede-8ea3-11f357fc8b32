# 核心数据模型规范

## 📋 模型概述

本文档定义了CheeStack系统中的核心数据模型，包括用户、内容、学习、同步等领域的数据结构。所有前后端实现都应严格遵循这些模型定义。

## 👤 用户领域模型

### User (用户)
```typescript
interface User {
  id: string;                    // UUID，用户唯一标识
  mobile: string;                // 手机号，11位数字
  username?: string;             // 用户名，3-50字符
  email?: string;                // 邮箱地址
  avatar?: string;               // 头像URL
  passwordHash?: string;         // 密码哈希（仅后端）
  isActive: boolean;             // 账户是否激活
  createdAt: DateTime;           // 创建时间
  updatedAt: DateTime;           // 更新时间
  lastLoginAt?: DateTime;        // 最后登录时间
}
```

### UserConfig (用户配置)
```typescript
interface UserConfig {
  userId: string;                // 用户ID，外键
  theme: 'light' | 'dark' | 'system';  // 主题设置
  language: string;              // 语言设置，如 'zh-CN', 'en-US'
  biometricEnabled: boolean;     // 是否启用生物识别
  autoSync: boolean;             // 是否自动同步
  notificationEnabled: boolean;  // 是否启用通知
  isAutoPlayAudio: boolean;      // 是否自动播放音频
  isAutoPlayAiAudio: boolean;    // 是否自动播放AI音频
  reviewNumber: number;          // 每日复习数量
  studyNumber: number;           // 每日学习数量
  editingBookId?: string;        // 当前编辑的书籍ID
  currentStudyId?: string;       // 当前学习的书籍ID
  studyType: number;             // 学习类型
}
```

### Device (设备)
```typescript
interface Device {
  id: string;                    // UUID，设备唯一标识
  userId: string;                // 用户ID，外键
  deviceName: string;            // 设备名称，如 "iPhone 15 Pro"
  deviceType: 'ios' | 'android' | 'web';  // 设备类型
  deviceModel?: string;          // 设备型号
  osVersion?: string;            // 操作系统版本
  appVersion: string;            // 应用版本
  deviceToken?: string;          // 推送令牌
  deviceFingerprint?: string;    // 设备指纹
  isActive: boolean;             // 设备是否激活
  lastActive: DateTime;          // 最后活跃时间
  createdAt: DateTime;           // 创建时间
}
```

## 📚 内容领域模型

### Book (书籍)
```typescript
interface Book {
  id: string;                    // UUID，书籍唯一标识
  userId: string;                // 创建者用户ID
  name: string;                  // 书籍名称，1-100字符
  brief?: string;                // 书籍简介
  cover?: string;                // 封面图片URL
  privacy: 'private' | 'public' | 'vip';  // 隐私设置
  tags: string[];                // 标签列表
  cardCount: number;             // 卡片总数
  progress: number;              // 学习进度 0-100
  createdAt: DateTime;           // 创建时间
  updatedAt: DateTime;           // 更新时间
  syncedAt?: DateTime;           // 最后同步时间
  isDirty: boolean;              // 是否有未同步的更改
}
```

### Chapter (章节)
```typescript
interface Chapter {
  id: string;                    // UUID，章节唯一标识
  bookId: string;                // 所属书籍ID
  parentId?: string;             // 父章节ID（支持多级章节）
  name: string;                  // 章节名称
  description?: string;          // 章节描述
  order: number;                 // 排序序号
  level: number;                 // 章节层级，1-3
  cardCount: number;             // 包含的卡片数量
  completedCount: number;        // 已完成的卡片数量
  createdAt: DateTime;           // 创建时间
}
```

### Card (学习卡片)
```typescript
interface Card {
  id: string;                    // UUID，卡片唯一标识
  userId: string;                // 创建者用户ID
  bookId: string;                // 所属书籍ID
  chapterId?: string;            // 所属章节ID
  type: CardType;                // 卡片类型
  title: string;                 // 卡片标题
  question: string;              // 问题内容
  answer: string;                // 答案内容
  extra?: Record<string, any>;   // 扩展数据
  assets: CardAsset[];           // 关联的媒体资源
  tags: string[];                // 标签列表
  difficulty?: number;           // 难度等级 1-5
  importance?: number;           // 重要程度 1-5
  createdAt: DateTime;           // 创建时间
  updatedAt: DateTime;           // 更新时间
  version: number;               // 版本号
  isDirty: boolean;              // 是否有未同步的更改
}

type CardType = 'basic' | 'cloze' | 'choice' | 'image' | 'audio' | 'video';
```

### CardAsset (卡片资源)
```typescript
interface CardAsset {
  id: string;                    // UUID，资源唯一标识
  cardId: string;                // 所属卡片ID
  assetType: 'image' | 'audio' | 'video';  // 资源类型
  fileName: string;              // 文件名
  filePath: string;              // 文件路径
  fileSize: number;              // 文件大小（字节）
  mimeType: string;              // MIME类型
  duration?: number;             // 时长（音频/视频，秒）
  thumbnail?: string;            // 缩略图URL
  createdAt: DateTime;           // 创建时间
}
```

### Tag (标签)
```typescript
interface Tag {
  id: string;                    // UUID，标签唯一标识
  userId: string;                // 创建者用户ID
  name: string;                  // 标签名称
  color?: string;                // 标签颜色
  parentId?: string;             // 父标签ID（支持层级标签）
  usageCount: number;            // 使用次数
  createdAt: DateTime;           // 创建时间
}
```

## 🧠 学习领域模型

### FSRSCard (FSRS算法数据)
```typescript
interface FSRSCard {
  cardId: string;                // 卡片ID，主键
  userId: string;                // 用户ID
  stability: number;             // 记忆稳定性
  difficulty: number;            // 难度系数 1-10
  elapsedDays: number;           // 经过天数
  scheduledDays: number;         // 计划间隔天数
  reps: number;                  // 复习次数
  lapses: number;                // 遗忘次数
  state: FSRSState;              // 卡片状态
  lastReview?: DateTime;         // 最后复习时间
  due: DateTime;                 // 下次复习时间
}

type FSRSState = 'new' | 'learning' | 'review' | 'relearning';
```

### StudyRecord (学习记录)
```typescript
interface StudyRecord {
  id: string;                    // UUID，记录唯一标识
  userId: string;                // 用户ID
  cardId: string;                // 卡片ID
  rating: number;                // 用户评分 1-5
  state: FSRSState;              // 学习状态
  reviewTime: DateTime;          // 复习时间
  elapsedDays: number;           // 经过天数
  scheduledDays: number;         // 计划间隔天数
  stabilityBefore: number;       // 复习前稳定性
  stabilityAfter: number;        // 复习后稳定性
  difficultyBefore: number;      // 复习前难度
  difficultyAfter: number;       // 复习后难度
  studyDuration?: number;        // 学习时长（秒）
}
```

### FSRSParams (FSRS参数)
```typescript
interface FSRSParams {
  userId: string;                // 用户ID，主键
  w: number[];                   // 17个FSRS参数
  requestRetention: number;      // 目标记忆保持率
  maximumInterval: number;       // 最大间隔天数
  updatedAt: DateTime;           // 更新时间
}
```

### VoiceRecord (语音记录)
```typescript
interface VoiceRecord {
  id: string;                    // UUID，记录唯一标识
  userId: string;                // 用户ID
  cardId: string;                // 卡片ID
  language: string;              // 语言代码
  audioFile: string;             // 音频文件路径
  duration: number;              // 录音时长（秒）
  textRecognized: string;        // 识别的文本
  textExpected: string;          // 期望的文本
  accuracyScore: number;         // 准确度评分 0-100
  fluencyScore: number;          // 流畅度评分 0-100
  completenessScore: number;     // 完整度评分 0-100
  overallScore: number;          // 综合评分 0-100
  createdAt: DateTime;           // 创建时间
}
```

## 🔄 同步领域模型

### SyncRecord (同步记录)
```typescript
interface SyncRecord {
  id: string;                    // UUID，记录唯一标识
  userId: string;                // 用户ID
  deviceId: string;              // 设备ID
  tableName: string;             // 表名
  recordId: string;              // 记录ID
  action: 'create' | 'update' | 'delete';  // 操作类型
  status: SyncStatus;            // 同步状态
  clientVersion: number;         // 客户端版本号
  serverVersion: number;         // 服务端版本号
  dataBefore?: Record<string, any>;  // 变更前数据
  dataAfter?: Record<string, any>;   // 变更后数据
  syncAt?: DateTime;             // 同步时间
  createdAt: DateTime;           // 创建时间
}

type SyncStatus = 'pending' | 'syncing' | 'success' | 'failed' | 'conflict';
```

### ConflictLog (冲突日志)
```typescript
interface ConflictLog {
  id: string;                    // UUID，日志唯一标识
  syncRecordId: string;          // 同步记录ID
  conflictType: string;          // 冲突类型
  clientData: Record<string, any>;  // 客户端数据
  serverData: Record<string, any>;  // 服务端数据
  resolution: ConflictResolution;   // 解决方案
  resolvedData?: Record<string, any>;  // 解决后的数据
  resolvedAt?: DateTime;         // 解决时间
  createdAt: DateTime;           // 创建时间
}

type ConflictResolution = 'client_wins' | 'server_wins' | 'merge' | 'manual';
```

### SyncConfig (同步配置)
```typescript
interface SyncConfig {
  userId: string;                // 用户ID，主键
  autoSyncEnabled: boolean;      // 是否启用自动同步
  syncInterval: number;          // 同步间隔（秒）
  defaultConflictResolution: ConflictResolution;  // 默认冲突解决策略
  syncBooks: boolean;            // 是否同步书籍
  syncCards: boolean;            // 是否同步卡片
  syncRecords: boolean;          // 是否同步学习记录
  lastSyncAt?: DateTime;         // 最后同步时间
}
```

## 🔧 通用类型定义

### DateTime
```typescript
// 统一的时间类型，使用ISO 8601格式
type DateTime = string;  // "2024-01-20T10:30:00.000Z"
```

### ApiResponse
```typescript
interface ApiResponse<T = any> {
  success: boolean;              // 请求是否成功
  data: T | null;                // 响应数据
  message: string;               // 响应消息
  code: number;                  // 响应码
  timestamp: DateTime;           // 响应时间戳
}
```

### PaginationParams
```typescript
interface PaginationParams {
  page: number;                  // 页码，从1开始
  size: number;                  // 每页大小
  sort?: string;                 // 排序字段
  order?: 'asc' | 'desc';        // 排序方向
}
```

### PaginationResponse
```typescript
interface PaginationResponse<T> {
  items: T[];                    // 数据列表
  total: number;                 // 总数量
  page: number;                  // 当前页码
  size: number;                  // 每页大小
  totalPages: number;            // 总页数
  hasNext: boolean;              // 是否有下一页
  hasPrev: boolean;              // 是否有上一页
}
```

## 📝 数据验证规则

### 字段长度限制
```typescript
const ValidationRules = {
  user: {
    mobile: { pattern: /^1[3-9]\d{9}$/ },
    username: { minLength: 3, maxLength: 50 },
    email: { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
    password: { minLength: 8, maxLength: 50 }
  },
  book: {
    name: { minLength: 1, maxLength: 100 },
    brief: { maxLength: 500 }
  },
  card: {
    title: { minLength: 1, maxLength: 200 },
    question: { minLength: 1, maxLength: 10000 },
    answer: { minLength: 1, maxLength: 10000 }
  },
  tag: {
    name: { minLength: 1, maxLength: 20 }
  }
};
```

### 文件大小限制
```typescript
const FileSizeLimits = {
  image: 10 * 1024 * 1024,      // 10MB
  audio: 50 * 1024 * 1024,      // 50MB
  video: 100 * 1024 * 1024,     // 100MB
  avatar: 5 * 1024 * 1024       // 5MB
};
```

## 🔄 版本控制

### 模型版本
- **当前版本**: v1.0.0
- **兼容性**: 向后兼容至少2个主要版本
- **变更策略**: 新增字段可选，删除字段需要版本升级

### 变更记录
| 版本 | 日期 | 变更内容 |
|------|------|----------|
| v1.0.0 | 2024-01-20 | 初始版本，定义核心数据模型 |

---

**注意**：本数据模型规范是系统的数据基础，所有API接口、数据库设计、前端状态管理都应严格遵循这些模型定义。任何模型变更都需要更新相关的实现代码。

# 创作模块数据库设计

## 📋 概述

本文档详细说明创作模块的数据库设计，包括本地SQLite和云端PostgreSQL的表结构、索引设计和数据同步策略。采用本地优先的设计理念，确保数据的一致性和可靠性。

## 🏗️ 数据库架构

### 双数据库架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Client Side (Flutter)                    │
├─────────────────────────────────────────────────────────────┤
│                    Local SQLite Database                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   books     │  │   cards     │  │   card_assets       │  │
│  │   users     │  │   book_card │  │   sync_records      │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ Sync API
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Server Side (FastAPI)                     │
├─────────────────────────────────────────────────────────────┤
│                  Cloud PostgreSQL Database                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   books     │  │   cards     │  │   card_assets       │  │
│  │   users     │  │   book_card │  │   sync_logs         │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 📊 本地SQLite数据库设计

### 1. users表 (用户信息)

```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    server_id INTEGER,                    -- 服务器端用户ID
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    avatar_url VARCHAR(512),
    is_vip BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_at DATETIME,                   -- 最后同步时间
    is_dirty BOOLEAN DEFAULT 0            -- 是否有未同步的更改
);

-- 索引
CREATE INDEX idx_users_server_id ON users(server_id);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_is_dirty ON users(is_dirty);
```

### 2. books表 (书籍)

```sql
CREATE TABLE books (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    server_id INTEGER,                    -- 服务器端书籍ID
    user_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    brief TEXT,
    cover VARCHAR(512),
    privacy VARCHAR(20) DEFAULT 'free',   -- free, private, vip
    card_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_at DATETIME,                   -- 最后同步时间
    is_dirty BOOLEAN DEFAULT 0,           -- 是否有未同步的更改
    is_deleted BOOLEAN DEFAULT 0,         -- 软删除标记
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_books_user_id ON books(user_id);
CREATE INDEX idx_books_server_id ON books(server_id);
CREATE INDEX idx_books_user_created ON books(user_id, created_at DESC);
CREATE INDEX idx_books_user_name ON books(user_id, name);
CREATE INDEX idx_books_privacy ON books(privacy);
CREATE INDEX idx_books_is_dirty ON books(is_dirty);
CREATE INDEX idx_books_is_deleted ON books(is_deleted);

-- 全文搜索索引
CREATE VIRTUAL TABLE books_fts USING fts5(
    name, brief, content='books', content_rowid='id'
);

-- 触发器：维护全文搜索索引
CREATE TRIGGER books_fts_insert AFTER INSERT ON books BEGIN
    INSERT INTO books_fts(rowid, name, brief) 
    VALUES (new.id, new.name, new.brief);
END;

CREATE TRIGGER books_fts_update AFTER UPDATE ON books BEGIN
    UPDATE books_fts SET name = new.name, brief = new.brief 
    WHERE rowid = new.id;
END;

CREATE TRIGGER books_fts_delete AFTER DELETE ON books BEGIN
    DELETE FROM books_fts WHERE rowid = old.id;
END;
```

### 3. cards表 (卡片)

```sql
CREATE TABLE cards (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    server_id INTEGER,                    -- 服务器端卡片ID
    user_id INTEGER NOT NULL,
    type VARCHAR(50) DEFAULT 'basic',
    type_version INTEGER DEFAULT 1,
    title VARCHAR(200),
    question TEXT,
    answer TEXT,
    extra TEXT,                           -- JSON格式的扩展数据
    schedule_id INTEGER,
    difficulty REAL DEFAULT 0.0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_at DATETIME,                   -- 最后同步时间
    is_dirty BOOLEAN DEFAULT 0,           -- 是否有未同步的更改
    is_deleted BOOLEAN DEFAULT 0,         -- 软删除标记
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_cards_user_id ON cards(user_id);
CREATE INDEX idx_cards_server_id ON cards(server_id);
CREATE INDEX idx_cards_user_created ON cards(user_id, created_at DESC);
CREATE INDEX idx_cards_user_type ON cards(user_id, type);
CREATE INDEX idx_cards_title ON cards(title);
CREATE INDEX idx_cards_is_dirty ON cards(is_dirty);
CREATE INDEX idx_cards_is_deleted ON cards(is_deleted);

-- 全文搜索索引
CREATE VIRTUAL TABLE cards_fts USING fts5(
    title, question, answer, content='cards', content_rowid='id'
);

-- 触发器：维护全文搜索索引
CREATE TRIGGER cards_fts_insert AFTER INSERT ON cards BEGIN
    INSERT INTO cards_fts(rowid, title, question, answer) 
    VALUES (new.id, new.title, new.question, new.answer);
END;

CREATE TRIGGER cards_fts_update AFTER UPDATE ON cards BEGIN
    UPDATE cards_fts SET title = new.title, question = new.question, answer = new.answer 
    WHERE rowid = new.id;
END;

CREATE TRIGGER cards_fts_delete AFTER DELETE ON cards BEGIN
    DELETE FROM cards_fts WHERE rowid = old.id;
END;
```

### 4. book_card表 (书籍卡片关联)

```sql
CREATE TABLE book_card (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    card_id INTEGER NOT NULL,
    order_index INTEGER DEFAULT 0,        -- 卡片在书籍中的顺序
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    FOREIGN KEY (card_id) REFERENCES cards(id) ON DELETE CASCADE,
    UNIQUE(book_id, card_id)
);

-- 索引
CREATE INDEX idx_book_card_book_id ON book_card(book_id);
CREATE INDEX idx_book_card_card_id ON book_card(card_id);
CREATE INDEX idx_book_card_order ON book_card(book_id, order_index);
```

### 5. card_assets表 (卡片资源)

```sql
CREATE TABLE card_assets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    server_id INTEGER,                    -- 服务器端资源ID
    card_id INTEGER NOT NULL,
    asset_id VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,            -- image, audio, video, text
    name VARCHAR(200),
    url VARCHAR(512),
    text TEXT,
    is_correct BOOLEAN DEFAULT 0,
    file_size INTEGER DEFAULT 0,
    mime_type VARCHAR(100),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_at DATETIME,                   -- 最后同步时间
    is_dirty BOOLEAN DEFAULT 0,           -- 是否有未同步的更改
    
    FOREIGN KEY (card_id) REFERENCES cards(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_card_assets_card_id ON card_assets(card_id);
CREATE INDEX idx_card_assets_server_id ON card_assets(server_id);
CREATE INDEX idx_card_assets_asset_id ON card_assets(asset_id);
CREATE INDEX idx_card_assets_type ON card_assets(type);
CREATE INDEX idx_card_assets_is_dirty ON card_assets(is_dirty);
```

### 6. sync_records表 (同步记录)

```sql
CREATE TABLE sync_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    entity_type VARCHAR(50) NOT NULL,     -- book, card, card_asset
    entity_id INTEGER NOT NULL,
    local_id INTEGER,                     -- 本地ID
    server_id INTEGER,                    -- 服务器ID
    operation VARCHAR(20) NOT NULL,       -- create, update, delete
    status VARCHAR(20) DEFAULT 'pending', -- pending, success, failed
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_sync_records_status ON sync_records(status);
CREATE INDEX idx_sync_records_entity ON sync_records(entity_type, entity_id);
CREATE INDEX idx_sync_records_created ON sync_records(created_at);
```

## 🌐 云端PostgreSQL数据库设计

### 1. books表 (书籍)

```sql
CREATE TABLE books (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    brief TEXT,
    cover VARCHAR(512),
    privacy VARCHAR(20) DEFAULT 'free',
    card_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_books_user_id ON books(user_id);
CREATE INDEX idx_books_user_created ON books(user_id, created_at DESC);
CREATE INDEX idx_books_user_name ON books(user_id, name);
CREATE INDEX idx_books_privacy ON books(privacy);

-- 全文搜索索引
CREATE INDEX idx_books_search ON books USING gin(to_tsvector('english', name || ' ' || COALESCE(brief, '')));
```

### 2. cards表 (卡片)

```sql
CREATE TABLE cards (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    type VARCHAR(50) DEFAULT 'basic',
    type_version INTEGER DEFAULT 1,
    title VARCHAR(200),
    question TEXT,
    answer TEXT,
    extra JSONB DEFAULT '{}',
    schedule_id INTEGER,
    difficulty REAL DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_cards_user_id ON cards(user_id);
CREATE INDEX idx_cards_user_created ON cards(user_id, created_at DESC);
CREATE INDEX idx_cards_user_type ON cards(user_id, type);
CREATE INDEX idx_cards_title ON cards(title);

-- JSONB索引
CREATE INDEX idx_cards_extra ON cards USING gin(extra);

-- 全文搜索索引
CREATE INDEX idx_cards_search ON cards USING gin(
    to_tsvector('english', 
        COALESCE(title, '') || ' ' || 
        COALESCE(question, '') || ' ' || 
        COALESCE(answer, '')
    )
);
```

### 3. 数据库触发器

```sql
-- 自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_books_updated_at 
    BEFORE UPDATE ON books 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cards_updated_at 
    BEFORE UPDATE ON cards 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 自动更新书籍卡片数量
CREATE OR REPLACE FUNCTION update_book_card_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE books SET card_count = card_count + 1 WHERE id = NEW.book_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE books SET card_count = card_count - 1 WHERE id = OLD.book_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_book_card_count_trigger
    AFTER INSERT OR DELETE ON book_card
    FOR EACH ROW EXECUTE FUNCTION update_book_card_count();
```

## 🔄 数据同步策略

### 1. 同步状态管理

```sql
-- 本地同步状态枚举
-- pending: 等待同步
-- syncing: 正在同步
-- success: 同步成功
-- failed: 同步失败
-- conflict: 存在冲突

-- 冲突解决策略
-- local_wins: 本地优先
-- server_wins: 服务器优先
-- manual: 手动解决
```

### 2. 增量同步机制

```sql
-- 获取需要上传的本地数据
SELECT * FROM books 
WHERE is_dirty = 1 AND is_deleted = 0
ORDER BY updated_at ASC;

-- 获取需要下载的服务器数据
SELECT * FROM books 
WHERE user_id = ? AND updated_at > ?
ORDER BY updated_at ASC;

-- 冲突检测查询
SELECT 
    l.id as local_id,
    l.updated_at as local_updated,
    s.id as server_id,
    s.updated_at as server_updated
FROM books l
JOIN books s ON l.server_id = s.id
WHERE l.is_dirty = 1 
  AND l.updated_at < s.updated_at;
```

### 3. 数据一致性保证

```sql
-- 事务性同步操作
BEGIN TRANSACTION;

-- 1. 创建同步记录
INSERT INTO sync_records (entity_type, entity_id, operation, status)
VALUES ('book', ?, 'update', 'pending');

-- 2. 更新本地数据
UPDATE books SET 
    name = ?,
    brief = ?,
    updated_at = CURRENT_TIMESTAMP,
    is_dirty = 1
WHERE id = ?;

-- 3. 提交事务
COMMIT;
```

## 📈 性能优化

### 1. 查询优化

```sql
-- 使用复合索引优化常用查询
CREATE INDEX idx_books_user_privacy_created 
ON books(user_id, privacy, created_at DESC);

-- 使用部分索引优化特定条件查询
CREATE INDEX idx_books_dirty 
ON books(user_id, updated_at) 
WHERE is_dirty = 1;

-- 使用表达式索引优化搜索
CREATE INDEX idx_books_name_lower 
ON books(lower(name));
```

### 2. 分区策略

```sql
-- 按用户ID分区（适用于大量用户场景）
CREATE TABLE books_partitioned (
    LIKE books INCLUDING ALL
) PARTITION BY HASH (user_id);

-- 创建分区表
CREATE TABLE books_partition_0 PARTITION OF books_partitioned
FOR VALUES WITH (MODULUS 4, REMAINDER 0);

CREATE TABLE books_partition_1 PARTITION OF books_partitioned
FOR VALUES WITH (MODULUS 4, REMAINDER 1);
```

### 3. 缓存策略

```sql
-- 物化视图：用户统计信息
CREATE MATERIALIZED VIEW user_stats AS
SELECT 
    user_id,
    COUNT(*) as total_books,
    COUNT(CASE WHEN privacy = 'free' THEN 1 END) as public_books,
    SUM(card_count) as total_cards,
    MAX(updated_at) as last_activity
FROM books
WHERE is_deleted = 0
GROUP BY user_id;

-- 定期刷新物化视图
CREATE OR REPLACE FUNCTION refresh_user_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY user_stats;
END;
$$ LANGUAGE plpgsql;
```

## 🔒 数据安全

### 1. 数据加密

```sql
-- 敏感字段加密存储
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- 加密存储用户邮箱
UPDATE users SET email = pgp_sym_encrypt(email, 'encryption_key');

-- 解密查询
SELECT pgp_sym_decrypt(email::bytea, 'encryption_key') as email FROM users;
```

### 2. 访问控制

```sql
-- 行级安全策略
ALTER TABLE books ENABLE ROW LEVEL SECURITY;

-- 用户只能访问自己的书籍
CREATE POLICY books_user_policy ON books
FOR ALL TO app_user
USING (user_id = current_setting('app.current_user_id')::integer);
```

### 3. 审计日志

```sql
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    table_name VARCHAR(50) NOT NULL,
    operation VARCHAR(10) NOT NULL,
    user_id INTEGER,
    old_values JSONB,
    new_values JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 审计触发器
CREATE OR REPLACE FUNCTION audit_trigger()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_logs (table_name, operation, user_id, old_values, new_values)
    VALUES (
        TG_TABLE_NAME,
        TG_OP,
        COALESCE(NEW.user_id, OLD.user_id),
        CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
        CASE WHEN TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN row_to_json(NEW) ELSE NULL END
    );
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;
```

---

**注意**：本文档描述的数据库设计应根据实际业务需求和数据量进行调整。在生产环境中，还需要考虑备份策略、监控告警、容灾恢复等方面的设计。

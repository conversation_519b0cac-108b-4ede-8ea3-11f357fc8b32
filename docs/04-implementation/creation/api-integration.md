# 创作模块API集成指导

## 📋 概述

本文档详细说明创作模块前后端API集成方式、数据格式、错误处理和同步机制。采用RESTful API设计原则，支持本地优先的数据同步策略。

## 🔌 API架构设计

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Flutter Client                           │
├─────────────────────────────────────────────────────────────┤
│  API Services   │  Data Models    │  Error Handling         │
│  • BookService  │  • BookModel    │  • ErrorHandler        │
│  • CardService  │  • CardModel    │  • RetryPolicy         │
│  • SyncService  │  • ApiResponse  │  • NetworkDetector     │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ HTTP/HTTPS
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    FastAPI Server                           │
├─────────────────────────────────────────────────────────────┤
│  API Routes     │  Schemas        │  Middleware             │
│  • /books       │  • BookCreate   │  • Authentication       │
│  • /cards       │  • CardUpdate   │  • Rate Limiting        │
│  • /sync        │  • ApiResponse  │  • CORS                 │
└─────────────────────────────────────────────────────────────┘
```

### API版本管理
```
Base URL: https://api.cheestack.com/api/v1/
Version Header: X-API-Version: 1.0
```

## 📊 数据格式规范

### 1. 统一响应格式

```json
{
  "success": true,
  "data": {
    // 实际数据内容
  },
  "message": "操作成功",
  "code": 200,
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "uuid-string"
}
```

**错误响应格式**：
```json
{
  "success": false,
  "data": null,
  "message": "错误描述",
  "code": 400,
  "error_code": "VALIDATION_ERROR",
  "details": {
    "field": "name",
    "reason": "书籍名称不能为空"
  },
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "uuid-string"
}
```

### 2. 书籍数据格式

**BookModel (前端)**：
```dart
class BookModel {
  int? id;
  String? name;
  String? brief;
  String? cover;
  String? privacy;
  int? cardCount;
  String? createdAt;
  String? updatedAt;
  UserModel? user;
  
  // 本地字段
  int? serverId;
  bool isDirty;
  DateTime? syncedAt;
}
```

**BookResponse (后端)**：
```json
{
  "id": 1,
  "name": "Flutter开发指南",
  "brief": "全面的Flutter开发教程",
  "cover": "https://cdn.example.com/covers/book1.jpg",
  "privacy": "free",
  "card_count": 25,
  "view_count": 100,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z",
  "user": {
    "id": 1,
    "username": "developer",
    "avatar": "https://cdn.example.com/avatars/user1.jpg"
  }
}
```

### 3. 卡片数据格式

**CardModel (前端)**：
```dart
class CardModel {
  int? id;
  String? type;
  int? typeVersion;
  String? title;
  String? question;
  String? answer;
  Map<String, dynamic>? extra;
  List<CardAsset>? cardAssets;
  DateTime? createdAt;
  DateTime? updatedAt;
  
  // 本地字段
  int? serverId;
  bool isDirty;
  DateTime? syncedAt;
}
```

**CardResponse (后端)**：
```json
{
  "id": 1,
  "type": "basic",
  "type_version": 1,
  "title": "Flutter是什么？",
  "question": "请简述Flutter框架的特点",
  "answer": "Flutter是Google开发的跨平台UI框架...",
  "extra": {
    "difficulty": "easy",
    "tags": ["flutter", "基础"]
  },
  "card_assets": [
    {
      "id": 1,
      "asset_id": "img_001",
      "type": "image",
      "name": "Flutter架构图",
      "url": "https://cdn.example.com/images/flutter_arch.png",
      "is_correct": false
    }
  ],
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

## 🔗 API接口定义

### 1. 书籍管理API

#### 获取书籍列表
```http
GET /api/v1/books?skip=0&limit=20&search=flutter&privacy=free&order_by=-created_at
Authorization: Bearer {token}
```

**响应**：
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Flutter开发指南",
      // ... 其他字段
    }
  ],
  "pagination": {
    "total": 100,
    "skip": 0,
    "limit": 20,
    "has_more": true
  }
}
```

#### 创建书籍
```http
POST /api/v1/books
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "新书籍",
  "brief": "书籍简介",
  "cover": "https://example.com/cover.jpg",
  "privacy": "free"
}
```

#### 更新书籍
```http
PUT /api/v1/books/{book_id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "更新后的书名",
  "brief": "更新后的简介"
}
```

#### 删除书籍
```http
DELETE /api/v1/books/{book_id}
Authorization: Bearer {token}
```

### 2. 卡片管理API

#### 获取卡片列表
```http
GET /api/v1/cards?book_id=1&skip=0&limit=20&search=flutter&type=basic
Authorization: Bearer {token}
```

#### 创建卡片
```http
POST /api/v1/cards
Authorization: Bearer {token}
Content-Type: application/json

{
  "book_id": 1,
  "type": "basic",
  "type_version": 1,
  "title": "卡片标题",
  "question": "问题内容",
  "answer": "答案内容",
  "extra": {
    "difficulty": "medium"
  },
  "card_assets": [
    {
      "asset_id": "img_001",
      "type": "image",
      "name": "示例图片",
      "url": "https://example.com/image.jpg"
    }
  ]
}
```

### 3. 数据同步API

#### 批量同步数据
```http
POST /api/v1/sync/batch
Authorization: Bearer {token}
Content-Type: application/json

{
  "books": [
    {
      "local_id": 1,
      "operation": "create",
      "data": {
        "name": "本地创建的书籍",
        "brief": "简介"
      }
    }
  ],
  "cards": [
    {
      "local_id": 2,
      "operation": "update",
      "data": {
        "id": 10,
        "title": "更新的卡片标题"
      }
    }
  ]
}
```

**响应**：
```json
{
  "success": true,
  "data": {
    "books": [
      {
        "local_id": 1,
        "server_id": 100,
        "status": "success"
      }
    ],
    "cards": [
      {
        "local_id": 2,
        "server_id": 200,
        "status": "success"
      }
    ],
    "conflicts": []
  }
}
```

## 🔄 前端API服务实现

### 1. 基础HTTP客户端

```dart
class ApiClient {
  static final Dio _dio = Dio();
  static const String baseUrl = 'https://api.cheestack.com/api/v1';
  
  static void init() {
    _dio.options.baseUrl = baseUrl;
    _dio.options.connectTimeout = Duration(seconds: 10);
    _dio.options.receiveTimeout = Duration(seconds: 10);
    
    // 添加拦截器
    _dio.interceptors.add(AuthInterceptor());
    _dio.interceptors.add(ErrorInterceptor());
    _dio.interceptors.add(LogInterceptor());
  }
  
  static Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      return ApiResponse.fromResponse(response, fromJson);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
  
  static Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final response = await _dio.post(path, data: data);
      return ApiResponse.fromResponse(response, fromJson);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
}
```

### 2. 书籍API服务

```dart
class BookApiService {
  static const String _basePath = '/books';
  
  /// 获取书籍列表
  static Future<ApiResponse<List<BookModel>>> getBooks({
    int skip = 0,
    int limit = 20,
    String? search,
    String? privacy,
    String orderBy = '-created_at',
  }) async {
    final queryParams = {
      'skip': skip,
      'limit': limit,
      'order_by': orderBy,
    };
    
    if (search != null) queryParams['search'] = search;
    if (privacy != null) queryParams['privacy'] = privacy;
    
    return await ApiClient.get<List<BookModel>>(
      _basePath,
      queryParameters: queryParams,
      fromJson: (json) => (json['data'] as List)
          .map((item) => BookModel.fromJson(item))
          .toList(),
    );
  }
  
  /// 创建书籍
  static Future<ApiResponse<BookModel>> createBook({
    required String name,
    String? brief,
    String? cover,
    String privacy = 'free',
  }) async {
    final data = {
      'name': name,
      'brief': brief,
      'cover': cover,
      'privacy': privacy,
    };
    
    return await ApiClient.post<BookModel>(
      _basePath,
      data: data,
      fromJson: (json) => BookModel.fromJson(json['data']),
    );
  }
  
  /// 更新书籍
  static Future<ApiResponse<BookModel>> updateBook({
    required int bookId,
    String? name,
    String? brief,
    String? cover,
    String? privacy,
  }) async {
    final data = <String, dynamic>{};
    if (name != null) data['name'] = name;
    if (brief != null) data['brief'] = brief;
    if (cover != null) data['cover'] = cover;
    if (privacy != null) data['privacy'] = privacy;
    
    return await ApiClient.put<BookModel>(
      '$_basePath/$bookId',
      data: data,
      fromJson: (json) => BookModel.fromJson(json['data']),
    );
  }
  
  /// 删除书籍
  static Future<ApiResponse<void>> deleteBook(int bookId) async {
    return await ApiClient.delete('$_basePath/$bookId');
  }
}
```

### 3. 同步服务实现

```dart
class SyncService {
  static const String _basePath = '/sync';
  
  /// 批量同步数据
  static Future<ApiResponse<SyncResult>> batchSync({
    List<SyncItem>? books,
    List<SyncItem>? cards,
  }) async {
    final data = <String, dynamic>{};
    if (books != null) data['books'] = books.map((e) => e.toJson()).toList();
    if (cards != null) data['cards'] = cards.map((e) => e.toJson()).toList();
    
    return await ApiClient.post<SyncResult>(
      '$_basePath/batch',
      data: data,
      fromJson: (json) => SyncResult.fromJson(json['data']),
    );
  }
  
  /// 获取服务器更新
  static Future<ApiResponse<ServerUpdates>> getServerUpdates({
    DateTime? lastSyncTime,
  }) async {
    final queryParams = <String, dynamic>{};
    if (lastSyncTime != null) {
      queryParams['since'] = lastSyncTime.toIso8601String();
    }
    
    return await ApiClient.get<ServerUpdates>(
      '$_basePath/updates',
      queryParameters: queryParams,
      fromJson: (json) => ServerUpdates.fromJson(json['data']),
    );
  }
}
```

## 🚨 错误处理机制

### 1. 错误类型定义

```dart
enum ApiErrorType {
  network,          // 网络错误
  timeout,          // 超时错误
  authentication,   // 认证错误
  authorization,    // 授权错误
  validation,       // 验证错误
  server,          // 服务器错误
  unknown,         // 未知错误
}

class ApiError {
  final ApiErrorType type;
  final String message;
  final int? statusCode;
  final String? errorCode;
  final Map<String, dynamic>? details;
  
  const ApiError({
    required this.type,
    required this.message,
    this.statusCode,
    this.errorCode,
    this.details,
  });
}
```

### 2. 错误拦截器

```dart
class ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    ApiError apiError;
    
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        apiError = ApiError(
          type: ApiErrorType.timeout,
          message: '请求超时，请检查网络连接',
        );
        break;
        
      case DioExceptionType.connectionError:
        apiError = ApiError(
          type: ApiErrorType.network,
          message: '网络连接失败，请检查网络设置',
        );
        break;
        
      case DioExceptionType.badResponse:
        final statusCode = err.response?.statusCode;
        final responseData = err.response?.data;
        
        if (statusCode == 401) {
          apiError = ApiError(
            type: ApiErrorType.authentication,
            message: '认证失败，请重新登录',
            statusCode: statusCode,
          );
        } else if (statusCode == 403) {
          apiError = ApiError(
            type: ApiErrorType.authorization,
            message: '权限不足，无法执行此操作',
            statusCode: statusCode,
          );
        } else if (statusCode! >= 400 && statusCode < 500) {
          apiError = ApiError(
            type: ApiErrorType.validation,
            message: responseData?['message'] ?? '请求参数错误',
            statusCode: statusCode,
            errorCode: responseData?['error_code'],
            details: responseData?['details'],
          );
        } else {
          apiError = ApiError(
            type: ApiErrorType.server,
            message: '服务器错误，请稍后重试',
            statusCode: statusCode,
          );
        }
        break;
        
      default:
        apiError = ApiError(
          type: ApiErrorType.unknown,
          message: '未知错误：${err.message}',
        );
    }
    
    // 记录错误日志
    Console.error('API Error: ${apiError.message}', err);
    
    // 传递处理后的错误
    handler.next(DioException(
      requestOptions: err.requestOptions,
      error: apiError,
    ));
  }
}
```

### 3. 重试机制

```dart
class RetryInterceptor extends Interceptor {
  final int maxRetries;
  final Duration retryDelay;
  
  RetryInterceptor({
    this.maxRetries = 3,
    this.retryDelay = const Duration(seconds: 1),
  });
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final extra = err.requestOptions.extra;
    final retryCount = extra['retry_count'] ?? 0;
    
    // 判断是否需要重试
    if (retryCount < maxRetries && _shouldRetry(err)) {
      // 增加重试次数
      err.requestOptions.extra['retry_count'] = retryCount + 1;
      
      // 延迟后重试
      await Future.delayed(retryDelay * (retryCount + 1));
      
      try {
        final response = await Dio().fetch(err.requestOptions);
        handler.resolve(response);
        return;
      } catch (e) {
        // 重试失败，继续处理错误
      }
    }
    
    handler.next(err);
  }
  
  bool _shouldRetry(DioException err) {
    // 网络错误或服务器错误才重试
    return err.type == DioExceptionType.connectionError ||
           err.type == DioExceptionType.connectionTimeout ||
           (err.response?.statusCode != null && 
            err.response!.statusCode! >= 500);
  }
}
```

## 🔄 数据同步实现

### 1. 同步状态管理

```dart
class SyncManager {
  static final SyncManager _instance = SyncManager._internal();
  factory SyncManager() => _instance;
  SyncManager._internal();
  
  final StreamController<SyncStatus> _statusController = 
      StreamController<SyncStatus>.broadcast();
  
  Stream<SyncStatus> get statusStream => _statusController.stream;
  
  SyncStatus _currentStatus = SyncStatus.idle;
  SyncStatus get currentStatus => _currentStatus;
  
  void _updateStatus(SyncStatus status) {
    _currentStatus = status;
    _statusController.add(status);
  }
  
  /// 执行完整同步
  Future<bool> performFullSync() async {
    if (_currentStatus == SyncStatus.syncing) {
      return false; // 已在同步中
    }
    
    _updateStatus(SyncStatus.syncing);
    
    try {
      // 1. 上传本地更改
      await _uploadLocalChanges();
      
      // 2. 下载服务器更新
      await _downloadServerUpdates();
      
      // 3. 解决冲突
      await _resolveConflicts();
      
      _updateStatus(SyncStatus.success);
      return true;
    } catch (e) {
      _updateStatus(SyncStatus.failed);
      Console.error('Sync failed: $e');
      return false;
    }
  }
  
  /// 上传本地更改
  Future<void> _uploadLocalChanges() async {
    final bookDataService = Get.find<BookDataService>();
    final cardDataService = Get.find<CardDataService>();
    
    // 获取需要同步的数据
    final dirtyBooks = await bookDataService.getDirtyBooks();
    final dirtyCards = await cardDataService.getDirtyCards();
    
    if (dirtyBooks.isEmpty && dirtyCards.isEmpty) {
      return; // 没有需要同步的数据
    }
    
    // 构建同步请求
    final syncItems = <SyncItem>[];
    
    for (final book in dirtyBooks) {
      syncItems.add(SyncItem(
        localId: book.id!,
        serverId: book.serverId,
        operation: book.serverId == null ? 'create' : 'update',
        entityType: 'book',
        data: book.toJson(),
      ));
    }
    
    for (final card in dirtyCards) {
      syncItems.add(SyncItem(
        localId: card.id!,
        serverId: card.serverId,
        operation: card.serverId == null ? 'create' : 'update',
        entityType: 'card',
        data: card.toJson(),
      ));
    }
    
    // 批量同步
    final response = await SyncService.batchSync(
      books: syncItems.where((e) => e.entityType == 'book').toList(),
      cards: syncItems.where((e) => e.entityType == 'card').toList(),
    );
    
    if (response.isSuccess) {
      // 更新本地数据的服务器ID和同步状态
      await _updateLocalSyncStatus(response.data!);
    } else {
      throw Exception('Upload failed: ${response.message}');
    }
  }
}
```

### 2. 冲突解决策略

```dart
class ConflictResolver {
  /// 解决书籍冲突
  static Future<BookModel> resolveBookConflict(
    BookModel localBook,
    BookModel serverBook,
    ConflictResolution strategy,
  ) async {
    switch (strategy) {
      case ConflictResolution.localWins:
        return localBook;
        
      case ConflictResolution.serverWins:
        return serverBook;
        
      case ConflictResolution.merge:
        return _mergeBooks(localBook, serverBook);
        
      case ConflictResolution.manual:
        return await _showConflictDialog(localBook, serverBook);
    }
  }
  
  /// 合并书籍数据
  static BookModel _mergeBooks(BookModel local, BookModel server) {
    return BookModel(
      id: local.id,
      serverId: server.id,
      name: local.updatedAt!.isAfter(server.updatedAt!) 
          ? local.name : server.name,
      brief: local.updatedAt!.isAfter(server.updatedAt!) 
          ? local.brief : server.brief,
      cover: local.cover ?? server.cover,
      privacy: local.privacy,
      updatedAt: DateTime.now().toIso8601String(),
      isDirty: true, // 需要重新同步
    );
  }
  
  /// 显示冲突解决对话框
  static Future<BookModel> _showConflictDialog(
    BookModel local,
    BookModel server,
  ) async {
    final result = await Get.dialog<ConflictChoice>(
      ConflictResolutionDialog(
        title: '数据冲突',
        localData: local,
        serverData: server,
      ),
    );
    
    switch (result) {
      case ConflictChoice.useLocal:
        return local;
      case ConflictChoice.useServer:
        return server;
      case ConflictChoice.merge:
        return _mergeBooks(local, server);
      default:
        return local; // 默认使用本地数据
    }
  }
}
```

---

**注意**：本文档描述的API集成方案应根据实际项目需求进行调整。在生产环境中，还需要考虑API版本管理、限流策略、监控告警等方面的实现。


# AI协作开发标准工作流程

## 🧠 第1步：需求理解与严格批判分析

### 🔥 强制性批判分析要求

**我必须无条件执行以下严格分析，不得跳过任何一项：**

#### 1. 需求合理性质疑 (Mandatory Rationality Challenge)
- **技术可行性审查**：该需求在当前技术栈下是否真的可行？有哪些技术风险被忽视？
- **业务价值质疑**：这个需求真的有价值吗？还是只是用户的一时兴起？
- **优先级挑战**：在有限的时间内，这个需求真的比其他需求更重要吗？

#### 2. 逻辑漏洞识别 (Logic Flaw Detection)
- **因果关系检查**：用户的推理链条是否存在逻辑跳跃？
- **假设前提验证**：用户基于什么假设？这些假设是否成立？
- **矛盾点识别**：需求内部是否存在自相矛盾的地方？
- **遗漏要素发现**：用户忽略了哪些关键因素？

#### 3. 认知偏差检测 (Cognitive Bias Detection)
- **确认偏误**：用户是否只看到支持自己观点的信息？
- **锚定效应**：用户是否被第一印象或既有方案束缚？
- **过度自信**：用户是否高估了自己的判断能力？

#### 4. 破框思维挑战 (Out-of-the-Box Challenge)
- **根本问题重新定义**：用户真正要解决的问题是什么？是否在解决错误的问题？
- **替代方案探索**：有没有更简单、更优雅的解决方案？
- **反向思考**：如果完全相反的做法会怎样？
- **跨领域借鉴**：其他领域是如何解决类似问题的？

### 🚨 严厉质询标准 (Tough Questioning Standards)

**当发现以下情况时，我必须使用直接、尖锐的言辞进行质询：**

- **明显不合理的需求**：直接指出"这个想法有严重问题"
- **过度理想化的方案**：明确说明"你的预期过于乐观"
- **技术债务忽视**：严厉指出"你在制造技术债务"
- **用户体验灾难**：直言"这会严重损害用户体验"

### ✅ 批判分析输出要求

每次分析必须输出：
1. **问题识别清单**：发现的所有问题点
2. **风险评估报告**：技术风险、业务风险、时间风险
3. **替代方案建议**：至少提供2个不同的解决思路
4. **改进建议**：具体的优化方向

---

## 📋 第2步：文档状态全面审查

### 🔍 强制性文档扫描清单

**必须按顺序检查以下文档，不得遗漏：**

1. **需求文档审查**
   - `docs/01-requirements/business-requirements.md`
   - `docs/01-requirements/functional-requirements.md`
   - `docs/01-requirements/non-functional-requirements.md`
   - `docs/01-requirements/acceptance-criteria.md`

2. **规范文档审查**
   - `docs/02-specifications/api-specifications/`
   - `docs/02-specifications/data-models/`
   - `docs/02-specifications/ui-specifications/`

3. **架构文档审查**
   - `docs/03-architecture/system-architecture.md`

4. **实现文档审查**
   - `docs/04-implementation/*/README.md`
   - `docs/04-implementation/*/backend-implementation.md`
   - `docs/04-implementation/*/frontend-implementation.md`

### 📊 文档状态评估输出

必须输出：
- **文档完整性报告**：缺失的文档列表
- **一致性问题清单**：文档间的矛盾点
- **过时内容识别**：需要更新的内容
- **依赖关系图**：文档间的引用关系

---

## ✅ 第3步：任务清单制定

### 📝 任务拆解标准

**每个任务必须满足以下要求：**

- **SMART原则**：具体(Specific)、可测量(Measurable)、可实现(Achievable)、相关(Relevant)、有时限(Time-bound)
- **需求追溯**：每个任务必须标注对应的需求编号
- **依赖关系**：明确前置任务和后续任务
- **验收标准**：明确的完成标准和测试方法

### 🎯 任务优先级矩阵

使用四象限法则：
1. **紧急且重要**：立即执行
2. **重要不紧急**：计划执行
3. **紧急不重要**：委托或快速处理
4. **不紧急不重要**：删除或延后

---

## 📄 第4步：文档更新（先于代码）

### 🔄 文档更新顺序（严格按序执行）

1. **需求文档更新**（如有变更）
2. **技术规范文档更新**
3. **架构设计文档更新**
4. **实现指导文档更新**

### ✅ 文档质量检查清单

每个更新的文档必须通过：
- **完整性检查**：所有必要信息都已包含
- **一致性检查**：与其他文档保持一致
- **可执行性检查**：AI能够根据文档生成代码
- **可测试性检查**：包含明确的测试标准

---

## 💻 第5步：代码生成与实现

### 🎯 代码生成标准

**严格按照更新后的文档生成代码，必须包含：**

- **完整的业务逻辑实现**
- **全面的错误处理机制**
- **边界条件处理**
- **性能优化考虑**
- **安全性措施**

### 📋 代码质量要求

- **可读性**：清晰的命名和注释
- **可维护性**：模块化设计，低耦合高内聚
- **可扩展性**：支持未来功能扩展
- **可测试性**：便于编写和执行测试

---

## 🧪 第6步：自动化测试验证（全自动）

### ✅ 测试通用要求

- **100%自动化**：禁止任何需要人工干预的测试
- **完整覆盖**：正常流程、异常流程、边界情况
- **独立运行**：不依赖外部服务或生产数据
- **快速执行**：单个测试用例执行时间 < 10秒

### 🔹 后端测试标准（FastAPI）

```python
# 必须包含的测试类型
- 单元测试：pytest + 模拟依赖
- 集成测试：pytest + 测试数据库
- API测试：pytest + TestClient
- 性能测试：pytest-benchmark

# 测试覆盖要求
- 代码覆盖率 > 80%
- 分支覆盖率 > 70%
- 所有API端点必须测试
- 所有异常情况必须测试
```

### 🔸 前端测试标准（Flutter）

```dart
// 必须包含的测试类型
- Unit Test: 业务逻辑测试
- Widget Test: UI组件测试
- Integration Test: 端到端测试

// 测试覆盖要求
- 核心业务逻辑覆盖率 > 90%
- UI组件测试覆盖率 > 70%
- 关键用户流程必须有集成测试
```

### 🧾 测试文件组织规范

**严禁在根目录或不相关位置创建孤立测试文件！**

- **后端测试**：`tests/` 目录下，按模块组织
- **前端测试**：`test/` 目录下，镜像 `lib/` 结构
- **集成测试**：`integration_test/` 目录下
- **测试数据**：`tests/fixtures/` 或 `test/fixtures/`

---

## 📚 第7步：文档同步更新

### 🔄 文档同步内容

**根据实际实现更新以下内容：**

1. **代码示例更新**：确保文档中的代码示例与实际实现一致
2. **API文档更新**：更新接口说明、参数定义、响应格式
3. **使用说明更新**：更新安装、配置、使用指南
4. **变更日志记录**：记录本次变更的详细信息

### ✅ 最终一致性检查

- **文档与代码一致性**：所有示例代码可以正常运行
- **文档间一致性**：不同文档间的信息保持一致
- **版本信息一致性**：所有文档的版本信息同步更新

---

## 🚨 流程执行监控

### 📊 每步完成标准

每个步骤完成后必须输出：
- **执行摘要**：本步骤完成了什么
- **发现问题**：识别出的问题和风险
- **输出成果**：具体的交付物
- **下步准备**：为下一步骤准备的输入

### 🔍 质量门禁

每个步骤都有质量门禁，不达标不能进入下一步：
- **第1步**：必须识别出至少3个潜在问题
- **第2步**：必须完成所有相关文档的审查
- **第3步**：任务清单必须包含明确的验收标准
- **第4步**：文档必须通过一致性检查
- **第5步**：代码必须通过静态分析
- **第6步**：所有测试必须通过
- **第7步**：文档必须通过最终一致性检查

---

**注意：本工作流程是强制性的，每次对话都必须严格按照此流程执行，不得跳过任何步骤或降低标准！**

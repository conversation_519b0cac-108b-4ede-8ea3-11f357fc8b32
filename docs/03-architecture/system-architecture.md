# 系统架构设计

## 📋 架构概述

CheeStack系统采用**前后端分离 + 本地优先**的架构设计，基于需求驱动的技术选型，确保满足所有功能需求和非功能需求。

## 🎯 架构驱动需求

### 业务需求驱动
- **BR-002**: 数据安全和隐私 → 本地优先架构 + 端到端加密
- **BR-003**: 多设备无缝体验 → 数据同步架构 + 冲突解决
- **BR-004**: 降低使用门槛 → 离线优先 + 简化认证

### 非功能需求驱动
- **NFR-PERF-001**: API响应时间<500ms → 缓存策略 + 数据库优化
- **NFR-PERF-002**: 支持1000+并发 → 微服务架构 + 负载均衡
- **NFR-SEC-001**: 身份认证安全 → JWT + 设备绑定
- **NFR-AVAIL-002**: 离线可用性 → 本地SQLite + 数据同步

## 🏗️ 整体架构设计

### 架构分层
```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (Presentation)                    │
│  Flutter App (iOS/Android/Web) + Material Design 3        │
├─────────────────────────────────────────────────────────────┤
│                    应用服务层 (Application)                    │
│  GetX状态管理 + 业务用例 + 本地服务                              │
├─────────────────────────────────────────────────────────────┤
│                    领域服务层 (Domain)                        │
│  业务实体 + 领域服务 + FSRS算法引擎                             │
├─────────────────────────────────────────────────────────────┤
│                    基础设施层 (Infrastructure)                 │
│  本地存储(SQLite) + 网络服务 + 语音引擎                         │
└─────────────────────────────────────────────────────────────┘
                              ↕ HTTPS/WSS
┌─────────────────────────────────────────────────────────────┐
│                    API网关层 (Gateway)                       │
│  FastAPI + 认证中间件 + 限流中间件 + 日志中间件                   │
├─────────────────────────────────────────────────────────────┤
│                    微服务层 (Microservices)                  │
│  认证服务 + 内容服务 + 学习服务 + 同步服务                        │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (Data Access)                   │
│  Tortoise ORM + 数据库连接池 + 缓存抽象                        │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层 (Storage)                       │
│  PostgreSQL + Redis + 对象存储                              │
└─────────────────────────────────────────────────────────────┘
```

### 核心设计原则
1. **本地优先**: 所有核心功能优先使用本地数据和计算
2. **数据同步**: 网络恢复后自动同步，保证多设备一致性
3. **微服务化**: 后端按业务领域拆分为独立服务
4. **事件驱动**: 使用事件机制解耦服务间依赖

## 📱 前端架构设计

### 技术栈选择
```yaml
# 基于需求的技术选型
框架选择: Flutter 3.16+
  理由: 跨平台支持(NFR-COMPAT-001) + 高性能(NFR-PERF-001)
  
状态管理: GetX 4.6+
  理由: 响应式编程 + 依赖注入 + 路由管理一体化
  
本地存储: SQLite (sqflite 2.3+)
  理由: 离线优先(NFR-AVAIL-002) + 数据安全(NFR-SEC-003)
  
网络请求: Dio 5.3+
  理由: 拦截器支持 + 错误处理 + 缓存策略
  
语音引擎: Sherpa-ONNX
  理由: 本地处理(NFR-AVAIL-002) + 多语言支持(FR-VOICE-001)
```

### 目录结构设计
```
lib/
├── core/                    # 核心基础设施
│   ├── di/                 # 依赖注入 (GetIt)
│   ├── database/           # 本地数据库配置
│   ├── network/            # 网络客户端配置
│   ├── storage/            # 本地存储管理
│   └── constants/          # 常量定义
├── features/               # 按业务功能组织
│   ├── auth/              # 认证功能 (FR-AUTH-*)
│   │   ├── data/          # 数据层：本地+远程数据源
│   │   ├── domain/        # 领域层：实体+用例+仓储接口
│   │   └── presentation/  # 表现层：UI+控制器+状态管理
│   ├── content/           # 内容管理 (FR-CONTENT-*)
│   ├── learning/          # 学习功能 (FR-FSRS-*)
│   ├── voice/             # 语音学习 (FR-VOICE-*)
│   └── sync/              # 数据同步 (FR-SYNC-*)
├── shared/                # 共享组件
│   ├── widgets/           # 通用UI组件
│   ├── utils/             # 工具函数
│   └── models/            # 共享数据模型
└── main.dart              # 应用入口
```

### 数据流架构
```
UI事件 → Controller → UseCase → Repository → DataSource
  ↓         ↓         ↓          ↓           ↓
状态更新 ← 状态管理 ← 业务逻辑 ← 数据抽象 ← 本地/远程数据
```

## 🖥️ 后端架构设计

### 微服务拆分
```yaml
# 基于业务领域的服务拆分
认证服务 (auth-service):
  职责: 用户认证、设备管理、权限控制
  对应需求: FR-AUTH-*, NFR-SEC-*
  
内容服务 (content-service):
  职责: 书籍管理、卡片创作、标签管理
  对应需求: FR-CONTENT-*
  
学习服务 (learning-service):
  职责: FSRS算法、学习记录、统计分析
  对应需求: FR-FSRS-*, FR-ANALYTICS-*
  
同步服务 (sync-service):
  职责: 数据同步、冲突解决、版本管理
  对应需求: FR-SYNC-*
  
语音服务 (voice-service):
  职责: 语音识别、发音评估、音频处理
  对应需求: FR-VOICE-*
```

### 服务间通信
```yaml
# 同步通信
HTTP/REST: 用户请求的实时响应
gRPC: 服务间高性能调用

# 异步通信  
消息队列: 数据同步、事件通知
WebSocket: 实时数据推送
```

### 数据库设计
```yaml
# 数据库分离策略
主数据库 (PostgreSQL):
  - 用户数据、内容数据、配置数据
  - 支持ACID事务、复杂查询
  
缓存数据库 (Redis):
  - 会话数据、临时数据、计数器
  - 支持高并发读写、数据过期
  
时序数据库 (InfluxDB):
  - 学习记录、性能指标、用户行为
  - 支持时间序列分析、数据压缩
```

## 🔄 数据同步架构

### 同步策略设计
```yaml
# 基于需求FR-SYNC-001的同步架构
本地优先策略:
  - 所有操作优先在本地完成
  - 本地数据立即生效，用户无感知
  - 后台异步同步到云端
  
增量同步策略:
  - 只同步变更的数据，减少网络传输
  - 基于时间戳和版本号的冲突检测
  - 支持断点续传和重试机制
  
冲突解决策略:
  - 自动解决：最后修改时间优先
  - 手动解决：用户选择保留版本
  - 合并解决：智能合并非冲突字段
```

### 同步流程设计
```
本地变更 → 生成同步记录 → 上传到云端 → 推送到其他设备
    ↓                                           ↓
本地应用 ← 冲突检测解决 ← 接收云端推送 ← 其他设备接收
```

## 🔐 安全架构设计

### 认证授权架构
```yaml
# 基于需求NFR-SEC-001的安全设计
多层认证:
  - 第一层：用户身份认证 (手机号/密码/生物识别)
  - 第二层：设备绑定验证 (JWT包含设备ID)
  - 第三层：权限控制验证 (RBAC权限模型)
  
令牌管理:
  - 访问令牌：15分钟过期，用于API调用
  - 刷新令牌：7天过期，用于令牌刷新
  - 设备令牌：永久有效，用于设备识别
```

### 数据安全架构
```yaml
# 基于需求NFR-SEC-002/003的数据保护
传输安全:
  - HTTPS/TLS 1.3强制加密
  - 证书固定防中间人攻击
  - API签名验证防篡改
  
存储安全:
  - 本地敏感数据AES-256加密
  - 数据库连接加密
  - 密码bcrypt哈希存储
```

## ⚡ 性能架构设计

### 缓存策略
```yaml
# 基于需求NFR-PERF-001的性能优化
多级缓存:
  - L1缓存：应用内存缓存 (GetX缓存)
  - L2缓存：本地数据库缓存 (SQLite)
  - L3缓存：分布式缓存 (Redis)
  
缓存策略:
  - 用户数据：写入时更新，TTL=1小时
  - 学习数据：实时更新，永不过期
  - 静态数据：启动时加载，版本控制更新
```

### 数据库优化
```yaml
# 基于需求NFR-PERF-003的数据库设计
索引策略:
  - 主键索引：所有表的主键
  - 唯一索引：用户手机号、邮箱
  - 复合索引：用户ID+创建时间
  - 全文索引：卡片内容搜索
  
分库分表:
  - 用户维度：按用户ID哈希分表
  - 时间维度：学习记录按月分表
  - 读写分离：主库写入，从库查询
```

## 📊 监控架构设计

### 监控体系
```yaml
# 基于需求NFR-MAINT-002的监控设计
应用监控:
  - 性能指标：响应时间、吞吐量、错误率
  - 业务指标：用户活跃度、学习效果、转化率
  - 用户体验：页面加载时间、操作成功率
  
基础设施监控:
  - 系统指标：CPU、内存、磁盘、网络
  - 数据库指标：连接数、查询性能、锁等待
  - 缓存指标：命中率、内存使用、网络延迟
```

### 告警策略
```yaml
# 分级告警机制
P0级别 (立即处理):
  - 系统不可用、数据丢失、安全事件
  - 通知方式：电话 + 短信 + 邮件
  
P1级别 (1小时内):
  - 性能严重下降、功能异常
  - 通知方式：短信 + 邮件 + IM
  
P2级别 (4小时内):
  - 性能轻微下降、非核心功能异常
  - 通知方式：邮件 + IM
```

## 🚀 部署架构设计

### 容器化部署
```yaml
# 基于需求NFR-SCALE-001的扩展设计
容器编排: Kubernetes
  - 自动扩缩容：基于CPU/内存使用率
  - 服务发现：基于DNS的服务注册
  - 负载均衡：基于权重的流量分发
  
服务网格: Istio
  - 流量管理：灰度发布、蓝绿部署
  - 安全策略：mTLS、访问控制
  - 可观测性：分布式追踪、指标收集
```

### 环境隔离
```yaml
# 多环境部署策略
开发环境 (dev):
  - 单机部署，快速迭代
  - 模拟数据，功能验证
  
测试环境 (test):
  - 集群部署，性能测试
  - 真实数据，集成验证
  
生产环境 (prod):
  - 高可用部署，容灾备份
  - 生产数据，监控告警
```

## 🔧 技术选型总结

### 前端技术栈
| 技术 | 版本 | 选择理由 | 对应需求 |
|------|------|----------|----------|
| Flutter | 3.16+ | 跨平台、高性能 | NFR-COMPAT-001, NFR-PERF-001 |
| GetX | 4.6+ | 状态管理、依赖注入 | 开发效率、代码质量 |
| SQLite | 3.40+ | 本地存储、离线支持 | NFR-AVAIL-002, NFR-SEC-003 |
| Sherpa-ONNX | 1.9+ | 本地语音识别 | FR-VOICE-001, NFR-AVAIL-002 |

### 后端技术栈
| 技术 | 版本 | 选择理由 | 对应需求 |
|------|------|----------|----------|
| FastAPI | 0.104+ | 高性能、自动文档 | NFR-PERF-002, 开发效率 |
| PostgreSQL | 15+ | ACID事务、复杂查询 | 数据一致性、查询性能 |
| Redis | 7.0+ | 高性能缓存 | NFR-PERF-001, 会话管理 |
| Docker | 24+ | 容器化部署 | NFR-SCALE-001, 环境一致性 |

---

**注意**：本系统架构设计完全基于需求驱动，每个技术选型和架构决策都能追溯到具体的业务需求或非功能需求。架构设计应该随着需求变化而演进，保持架构与需求的一致性。
